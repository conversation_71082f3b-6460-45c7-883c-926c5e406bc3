@tailwind base;
@tailwind components;
@tailwind utilities;

/* Glassmorphism Variables */
:root {
  /* Light mode glassmorphism */
  --glass-bg-light: rgba(255, 255, 255, 0.2);
  --glass-border-light: rgba(255, 255, 255, 0.3);
  --glass-text-light: rgba(0, 0, 26, 0.9);
  --glass-hover-light: rgba(255, 255, 255, 0.3);

  /* Dark mode glassmorphism */
  --glass-bg-dark: rgba(255, 255, 255, 0.1);
  --glass-border-dark: rgba(255, 255, 255, 0.2);
  --glass-text-dark: rgba(255, 255, 255, 0.9);
  --glass-hover-dark: rgba(255, 255, 255, 0.15);
}

/* Dynamic glassmorphism classes */
.glass-light {
  --glass-bg: var(--glass-bg-light);
  --glass-border: var(--glass-border-light);
  --glass-text: var(--glass-text-light);
  --glass-hover: var(--glass-hover-light);
}

.glass-dark {
  --glass-bg: var(--glass-bg-dark);
  --glass-border: var(--glass-border-dark);
  --glass-text: var(--glass-text-dark);
  --glass-hover: var(--glass-hover-dark);
}

/* Glassmorphism utility classes */
.glass-button {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--glass-text);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  transition: all 0.2s ease-in-out;
}

.glass-button:hover {
  background: var(--glass-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Custom scrollbar for glassmorphism */
.glass-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.glass-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.glass-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.glass-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Futuristic Glassmorphism Card Animations */
@keyframes holographic-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes aurora-flow {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateX(50%) translateY(50%) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(360deg);
    opacity: 0.3;
  }
}

@keyframes neon-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3), 0 0 40px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.5), 0 0 60px rgba(59, 130, 246, 0.4);
  }
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes chip-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes contactless-wave {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Futuristic Card Utilities */
.futuristic-card {
  position: relative;
  overflow: hidden;
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.futuristic-card:hover::before {
  left: 100%;
}

.holographic-text {
  background: linear-gradient(
    45deg,
    #ff006e,
    #8338ec,
    #3a86ff,
    #06ffa5,
    #ffbe0b,
    #ff006e
  );
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 3s ease-in-out infinite;
}

.aurora-background {
  background: linear-gradient(
    45deg,
    rgba(147, 51, 234, 0.3),
    rgba(59, 130, 246, 0.3),
    rgba(16, 185, 129, 0.3),
    rgba(245, 101, 101, 0.3)
  );
  background-size: 400% 400%;
  animation: aurora-flow 8s ease-in-out infinite;
}

.neon-border {
  border: 2px solid transparent;
  background: linear-gradient(45deg, rgba(147, 51, 234, 0.5), rgba(59, 130, 246, 0.5)) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* ATM Card Glassmorphism Effects */
.atm-card-hover {
  position: relative;
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.atm-card-hover:hover {
  transform: scale(1.05) translateY(-8px);
  box-shadow:
    0 35px 70px -12px rgba(0, 0, 0, 0.9),
    0 0 0 1px rgba(0, 0, 26, 0.4),
    inset 0 1px 0 rgba(0, 0, 26, 0.3),
    0 0 50px rgba(0, 0, 26, 0.6),
    0 12px 40px 0 rgba(0, 0, 26, 0.8) !important;
}

.atm-card-hover:hover::before {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 20px;
  background: linear-gradient(
    45deg,
    rgba(0, 0, 26, 0.4),
    rgba(0, 0, 26, 0.2),
    rgba(0, 0, 26, 0.4)
  );
  filter: blur(15px);
  z-index: -1;
  opacity: 1;
  animation: atm-glow 2s ease-in-out infinite alternate;
}

@keyframes atm-glow {
  0% {
    opacity: 0.5;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* ATM Card Glassmorphism Texture */
.atm-card-hover::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 26, 0.1) 0%,
    transparent 25%,
    transparent 75%,
    rgba(0, 0, 26, 0.05) 100%
  );
  pointer-events: none;
  z-index: 1;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

/* Responsive Viewport Setup */
html {
  height: 100%;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: white;
  color: #00001a;
  transition: background-color 0.5s ease, color 0.5s ease;
}

body.dark {
  background-color: #00001a;
  color: white;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Dynamic Glow Animations */
@keyframes pulse-glow-dark {
  0% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.1), 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2), 0 0 30px rgba(59, 130, 246, 0.1), 0 6px 20px rgba(0, 0, 0, 0.15);
  }
  100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.1), 0 4px 15px rgba(0, 0, 0, 0.1);
  }
}

@keyframes pulse-glow-light {
  0% {
    box-shadow: 0 0 8px rgba(21, 58, 168, 0.2), 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  50% {
    box-shadow: 0 0 16px rgba(21, 58, 168, 0.3), 0 0 24px rgba(21, 58, 168, 0.15), 0 6px 18px rgba(0, 0, 0, 0.12);
  }
  100% {
    box-shadow: 0 0 8px rgba(21, 58, 168, 0.2), 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

@keyframes glow-breathe {
  0% {
    box-shadow: 0 0 15px rgba(41, 46, 73, 0.15), 0 4px 15px rgba(41, 46, 73, 0.1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(41, 46, 73, 0.25), 0 0 50px rgba(41, 46, 73, 0.1), 0 8px 25px rgba(41, 46, 73, 0.2);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 15px rgba(41, 46, 73, 0.15), 0 4px 15px rgba(41, 46, 73, 0.1);
    transform: scale(1);
  }
}

/* Hover enhancement animation */
.dynamic-glow-hover {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.dynamic-glow-hover:hover {
  animation: glow-breathe 2s ease-in-out infinite;
}

/* Soft Glow Effect for Dark Mode Only */
.soft-glow-dark {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Hover Glow Effect - Only on Cursor Hover */
.hover-glow-dark:hover {
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(59, 130, 246, 0.15),
    0 0 40px rgba(59, 130, 246, 0.1) !important;
  transition: box-shadow 0.3s ease !important;
}

/* Soft Glow Effect for Light Mode */
.soft-glow-light {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Hover Glow Effect for Light Mode - Only on Cursor Hover */
.hover-glow-light:hover {
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 0 20px rgba(0, 0, 26, 0.15),
    0 0 40px rgba(0, 0, 26, 0.1) !important;
  transition: box-shadow 0.3s ease !important;
}

/* Podcast Tile Hover Effects - Clean Implementation */
.podcast-tile {
  transition: box-shadow 0.3s ease !important;
  box-shadow: none !important;
}

.podcast-tile:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.podcast-tile.light-mode {
  box-shadow: none !important;
}

.podcast-tile.light-mode:hover {
  box-shadow: 0 0 20px rgba(0, 0, 26, 0.2), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Global text color overrides for light mode */
body:not(.dark) .text-gray-600 {
  color: rgba(0, 0, 26, 0.7) !important;
}

/* Notification animations */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

body:not(.dark) .text-gray-700 {
  color: rgba(0, 0, 26, 0.8) !important;
}

body:not(.dark) .text-gray-800 {
  color: rgba(0, 0, 26, 0.9) !important;
}

body:not(.dark) .text-gray-900 {
  color: rgba(0, 0, 26, 1) !important;
}

body:not(.dark) .text-gray-500 {
  color: rgba(0, 0, 26, 0.6) !important;
}

body:not(.dark) .text-gray-400 {
  color: rgba(0, 0, 26, 0.5) !important;
}

/* Remove any potential ">" symbols from pseudo-elements */
button::before,
button::after,
.flex::before,
.flex::after,
span::before,
span::after,
div::before,
div::after {
  content: none !important;
}

/* Specifically target any elements that might have ">" content */
*::before {
  content: none !important;
}

*::after {
  content: none !important;
}

/* Responsive Layout Utilities */
.responsive-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.responsive-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
}

/* Responsive breakpoints for grids */
@media (min-width: 640px) {
  .responsive-grid-sm {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-grid-md {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid-lg {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid-xl {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Ensure all containers fill viewport */
.viewport-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Responsive text scaling */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 0.875rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .responsive-text {
    font-size: 1rem;
  }
}

@media (min-width: 1025px) {
  .responsive-text {
    font-size: 1.125rem;
  }
}
