import React, { useState, useRef, useEffect } from 'react'
import { Camera, CameraOff, RotateCcw, CheckCircle, AlertCircle } from 'lucide-react'

const PhoneCamera = () => {
  const [cameraActive, setCameraActive] = useState(false)
  const [cameraError, setCameraError] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const videoRef = useRef(null)
  const streamRef = useRef(null)

  useEffect(() => {
    // Simulate connection to demo session
    const timer = setTimeout(() => {
      setIsConnected(true)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  const startCamera = async () => {
    try {
      setCameraError('')
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera for keyboard/screen view
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        streamRef.current = stream
        setCameraActive(true)
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      setCameraError('Unable to access camera. Please check permissions.')
    }
  }

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    setCameraActive(false)
  }

  const switchCamera = async () => {
    stopCamera()
    // Small delay before starting new camera
    setTimeout(startCamera, 500)
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="bg-black p-4 text-center">
        <h1 className="text-xl font-bold">SynapSolver Demo Camera</h1>
        <p className="text-sm text-gray-300 mt-1">
          Position your phone to capture keyboard and screen actions
        </p>
      </div>

      {/* Connection Status */}
      <div className="p-4 bg-gray-800">
        <div className="flex items-center justify-center gap-2">
          {isConnected ? (
            <>
              <CheckCircle size={20} className="text-green-400" />
              <span className="text-green-400">Connected to Demo Session</span>
            </>
          ) : (
            <>
              <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-blue-400">Connecting to Demo Session...</span>
            </>
          )}
        </div>
      </div>

      {/* Camera View */}
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        {!cameraActive ? (
          <div className="text-center max-w-md">
            <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <Camera size={40} className="text-gray-400" />
            </div>
            <h2 className="text-2xl font-bold mb-4">Start Phone Camera</h2>
            <p className="text-gray-300 mb-6">
              Position your phone to capture your keyboard and screen during the demo. 
              Use the back camera for the best view.
            </p>
            {cameraError && (
              <div className="bg-red-900/50 border border-red-500 rounded-lg p-3 mb-4">
                <div className="flex items-center gap-2">
                  <AlertCircle size={16} className="text-red-400" />
                  <span className="text-red-400 text-sm">{cameraError}</span>
                </div>
              </div>
            )}
            <button
              onClick={startCamera}
              disabled={!isConnected}
              className={`px-8 py-3 rounded-lg font-medium transition-all duration-300 ${
                isConnected
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'
              }`}
            >
              {isConnected ? 'Start Camera' : 'Waiting for Connection...'}
            </button>
          </div>
        ) : (
          <div className="w-full max-w-4xl">
            <div className="relative bg-black rounded-lg overflow-hidden">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-auto"
                style={{ maxHeight: '60vh' }}
              />
              
              {/* Camera Controls Overlay */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-4">
                <button
                  onClick={switchCamera}
                  className="p-3 bg-gray-800/80 hover:bg-gray-700/80 rounded-full transition-colors"
                  title="Switch Camera"
                >
                  <RotateCcw size={20} />
                </button>
                <button
                  onClick={stopCamera}
                  className="p-3 bg-red-600/80 hover:bg-red-700/80 rounded-full transition-colors"
                  title="Stop Camera"
                >
                  <CameraOff size={20} />
                </button>
              </div>

              {/* Recording Indicator */}
              <div className="absolute top-4 left-4 flex items-center gap-2 bg-black/60 px-3 py-2 rounded-lg">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Recording</span>
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-4 p-4 bg-gray-800 rounded-lg">
              <h3 className="font-semibold mb-2">Camera Positioning Tips:</h3>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Position phone to capture both keyboard and screen</li>
                <li>• Ensure good lighting for clear visibility</li>
                <li>• Keep phone steady during the demo</li>
                <li>• Make sure your hands and typing are visible</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 bg-black text-center text-sm text-gray-400">
        Keep this page open during the entire demo session
      </div>
    </div>
  )
}

export default PhoneCamera
