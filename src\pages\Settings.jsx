import React, { useState, useEffect } from 'react'
import {
  Mail,
  Bell,
  Shield,
  FileText,
  Lock,
  Smartphone,
  Calendar,
  MessageSquare,
  TrendingUp,
  Award,
  CreditCard,
  AlertTriangle,
  Users,
  DollarSign,
  Check,
  X,
  Clock
} from 'lucide-react'
const Settings = ({ darkMode }) => {
  const [selectedCountry, setSelectedCountry] = useState('')
  const [selectedTimezone, setSelectedTimezone] = useState('')

  // Active section state
  const [activeSection, setActiveSection] = useState('email')

  // Settings states
  const [emailAlerts, setEmailAlerts] = useState({
    newMessages: true,
    sessionReminders: true,
    paymentUpdates: false,
    securityAlerts: true
  })

  const [notifications, setNotifications] = useState({
    emailAlerts: true,
    calendarReminders: true,
    pushNotifications: false,
    messageNotifications: true,
    weeklyDigest: true,
    achievements: true
  })

  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    showOnlineStatus: true,
    allowDirectMessages: true,
    shareAnalytics: false
  })

  const [mfa, setMfa] = useState({
    enabled: false,
    method: 'sms',
    backupCodes: false
  })

  // Policy click handlers
  const handlePolicyClick = (policyKey) => {
    // Show notification that policy is being opened
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-500 transform translate-x-full ${
      darkMode
        ? 'bg-blue-500/20 border border-blue-500/30 text-blue-400'
        : 'bg-[#00001a] border border-[#00001a] text-white'
    }`
    notification.innerHTML = `
      <div class="flex items-center gap-2">
        <span>📄</span>
        <span>Opening ${policyKey} policy...</span>
      </div>
    `
    document.body.appendChild(notification)

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)'
    }, 100)

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 500)
    }, 3000)

    // In a real app, this would open the policy document
    console.log(`Opening ${policyKey} policy`)
  }

  // MFA backup codes handler
  const handleGenerateBackupCodes = () => {
    // Show notification that backup codes are being generated
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-500 transform translate-x-full ${
      darkMode
        ? 'bg-green-500/20 border border-green-500/30 text-green-400'
        : 'bg-[#00001a] border border-[#00001a] text-white'
    }`
    notification.innerHTML = `
      <div class="flex items-center gap-2">
        <span>🔐</span>
        <span>Generating new backup codes...</span>
      </div>
    `
    document.body.appendChild(notification)

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)'
    }, 100)

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 500)
    }, 3000)

    // In a real app, this would generate new backup codes
    console.log('Generating new MFA backup codes')
  }



  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedCountry = localStorage.getItem('selectedCountry')
    const savedTimezone = localStorage.getItem('selectedTimezone')
    const savedEmailAlerts = localStorage.getItem('emailAlerts')
    const savedNotifications = localStorage.getItem('notifications')
    const savedPrivacy = localStorage.getItem('privacy')
    const savedMfa = localStorage.getItem('mfa')

    if (savedCountry) setSelectedCountry(savedCountry)
    if (savedTimezone) setSelectedTimezone(savedTimezone)
    if (savedEmailAlerts) setEmailAlerts(JSON.parse(savedEmailAlerts))
    if (savedNotifications) setNotifications(JSON.parse(savedNotifications))
    if (savedPrivacy) setPrivacy(JSON.parse(savedPrivacy))
    if (savedMfa) setMfa(JSON.parse(savedMfa))
  }, [])

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('selectedCountry', selectedCountry)
  }, [selectedCountry])

  useEffect(() => {
    localStorage.setItem('selectedTimezone', selectedTimezone)
  }, [selectedTimezone])

  useEffect(() => {
    localStorage.setItem('emailAlerts', JSON.stringify(emailAlerts))
  }, [emailAlerts])

  useEffect(() => {
    localStorage.setItem('notifications', JSON.stringify(notifications))
  }, [notifications])

  useEffect(() => {
    localStorage.setItem('privacy', JSON.stringify(privacy))
  }, [privacy])

  useEffect(() => {
    localStorage.setItem('mfa', JSON.stringify(mfa))
  }, [mfa])
  


  return (
    <div className={`h-full w-full transition-all duration-500 ${
      darkMode ? 'bg-[#00001a]' : 'bg-gray-50'
    }`}>
      {/* Header */}
      <div className="p-6 pb-0">
        <div className="mb-6">
          <div>
            <p className={`text-sm mt-1 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Manage your account preferences and security settings
            </p>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className={`flex flex-wrap gap-3 p-2 rounded-lg ${
          darkMode
            ? ''
            : 'bg-white'
        }`}>
          {[
            { id: 'email', label: 'Email Alerts', icon: Mail },
            { id: 'notifications', label: 'Notifications', icon: Bell },
            { id: 'privacy', label: 'Privacy', icon: Shield },
            { id: 'policies', label: 'Policies', icon: FileText },
            { id: 'mfa', label: 'Security (MFA)', icon: Lock }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveSection(tab.id)}
                className={`flex items-center gap-2 px-5 py-3 rounded-lg font-medium transition-all duration-300 relative overflow-hidden shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeSection === tab.id
                    ? darkMode
                      ? 'bg-white text-black font-semibold'
                      : 'bg-[#00001a] text-white font-semibold'
                    : darkMode
                      ? 'bg-[#00001a] text-white font-normal hover:bg-[#00001a]/80'
                      : 'text-gray-600 hover:text-gray-900 bg-white hover:bg-gray-50 font-normal'
                }`}
                style={darkMode ? {
                  boxShadow: activeSection === tab.id
                    ? '0 0 20px rgba(59,130,246,0.15), inset 0 1px 0 rgba(255,255,255,0.1)'
                    : '0 0 15px rgba(59,130,246,0.1), inset 0 1px 0 rgba(255,255,255,0.05)',
                  backdropFilter: 'blur(5px)'
                } : {
                  boxShadow: activeSection === tab.id
                    ? '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeSection === tab.id
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255,255,255,0.15)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255,255,255,0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeSection === tab.id
                      ? '0 0 20px rgba(59,130,246,0.15), inset 0 1px 0 rgba(255,255,255,0.1)'
                      : '0 0 15px rgba(59,130,246,0.1), inset 0 1px 0 rgba(255,255,255,0.05)';
                  }
                }}
              >
                <Icon size={16} />
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      <div className="p-6 space-y-6">



        {/* Email Alerts Section */}
        {activeSection === 'email' && (
          <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl ${
            darkMode
              ? 'bg-white/3 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
              : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-[0_0_25px_rgba(0,0,0,0.15)]'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {}}>
            <div className="flex items-center gap-3 mb-6">
              <div className={`p-3 rounded-lg ${
                darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
              }`}>
                <Mail size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
              </div>
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Email Alerts
                </h2>
                <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Configure which email notifications you want to receive
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {[
                { key: 'newMessages', label: 'New Messages', desc: 'Get notified when you receive new messages' },
                { key: 'sessionReminders', label: 'Session Reminders', desc: 'Reminders for upcoming mentoring sessions' },
                { key: 'paymentUpdates', label: 'Payment Updates', desc: 'Notifications about payments and earnings' },
                { key: 'securityAlerts', label: 'Security Alerts', desc: 'Important security and account notifications' }
              ].map((item) => (
                <div key={item.key} className={`p-4 rounded-lg border transition-all duration-300 ${
                  darkMode
                    ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        {item.label}
                      </div>
                      <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                        {item.desc}
                      </div>
                    </div>
                    <button
                      onClick={() => setEmailAlerts(prev => ({ ...prev, [item.key]: !prev[item.key] }))}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ${
                        emailAlerts[item.key]
                          ? darkMode ? 'bg-blue-500' : 'bg-[#00001a]'
                          : darkMode ? 'bg-white/20' : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ${
                          emailAlerts[item.key] ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Notifications Section */}
        {activeSection === 'notifications' && (
          <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl ${
            darkMode
              ? 'bg-white/3 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
              : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-[0_0_25px_rgba(0,0,0,0.15)]'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {}}>
            <div className="flex items-center gap-3 mb-6">
              <div className={`p-3 rounded-lg ${
                darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
              }`}>
                <Bell size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
              </div>
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Notification Settings
                </h2>
                <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Manage all your notification preferences
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {[
                { key: 'emailAlerts', label: 'Email Alerts', desc: 'Receive notifications via email', icon: Mail },
                { key: 'calendarReminders', label: 'Calendar Reminders', desc: 'Calendar notifications with note reminders', icon: Calendar },
                { key: 'pushNotifications', label: 'Push Notifications', desc: 'Browser push notifications', icon: Smartphone },
                { key: 'messageNotifications', label: 'Message Notifications', desc: 'Instant message alerts', icon: MessageSquare },
                { key: 'weeklyDigest', label: 'Weekly Digest', desc: 'Weekly summary of your activity', icon: TrendingUp },
                { key: 'achievements', label: 'Achievement Notifications', desc: 'Celebrate your milestones', icon: Award }
              ].map((item) => {
                const Icon = item.icon
                return (
                  <div key={item.key} className={`p-4 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        <Icon size={20} className={darkMode ? 'text-white/70' : 'text-gray-600'} />
                        <div className="flex-1">
                          <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {item.label}
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                            {item.desc}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => setNotifications(prev => ({ ...prev, [item.key]: !prev[item.key] }))}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ${
                          notifications[item.key]
                            ? darkMode ? 'bg-blue-500' : 'bg-[#00001a]'
                            : darkMode ? 'bg-white/20' : 'bg-gray-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ${
                            notifications[item.key] ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Quick Actions */}
            <div className="mt-6 flex gap-3">
              <button
                onClick={() => setNotifications(prev => Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: true }), {}))}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  darkMode
                    ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                    : 'bg-[#00001a] text-white border border-[#00001a] hover:border-[#00001a]/80 hover:shadow-[0_0_15px_rgba(0,0,26,0.2)]'
                }`}
              >
                <Check size={16} />
                Enable All
              </button>
              <button
                onClick={() => setNotifications(prev => Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: false }), {}))}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  darkMode
                    ? 'bg-white/10 text-white/70 border border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                    : 'bg-gray-100 text-gray-700 border border-gray-300 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                }`}
              >
                <X size={16} />
                Disable All
              </button>
            </div>
          </div>
        )}

        {/* Privacy Section */}
        {activeSection === 'privacy' && (
          <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl ${
            darkMode
              ? 'bg-white/3 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
              : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-[0_0_25px_rgba(0,0,0,0.15)]'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {}}>
            <div className="flex items-center gap-3 mb-6">
              <div className={`p-3 rounded-lg ${
                darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
              }`}>
                <Shield size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
              </div>
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Privacy Settings
                </h2>
                <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Control your privacy and data sharing preferences
                </p>
              </div>
            </div>

            <div className="space-y-6">
              {/* Profile Visibility */}
              <div className={`p-4 rounded-lg border transition-all duration-300 ${
                darkMode
                  ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
              }`}>
                <div className="mb-3">
                  <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Profile Visibility
                  </div>
                  <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                    Who can see your profile information
                  </div>
                </div>
                <div className="space-y-2">
                  {[
                    { value: 'public', label: 'Public', desc: 'Anyone can see your profile' },
                    { value: 'members', label: 'Members Only', desc: 'Only platform members can see your profile' },
                    { value: 'private', label: 'Private', desc: 'Only you can see your profile' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="radio"
                        name="profileVisibility"
                        value={option.value}
                        checked={privacy.profileVisibility === option.value}
                        onChange={(e) => setPrivacy(prev => ({ ...prev, profileVisibility: e.target.value }))}
                        className={`w-4 h-4 ${darkMode ? 'text-blue-500' : 'text-[#00001a]'}`}
                      />
                      <div>
                        <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          {option.label}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                          {option.desc}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Privacy Toggles */}
              <div className="space-y-4">
                {[
                  { key: 'showOnlineStatus', label: 'Show Online Status', desc: 'Let others see when you\'re online' },
                  { key: 'allowDirectMessages', label: 'Allow Direct Messages', desc: 'Allow other users to message you directly' },
                  { key: 'shareAnalytics', label: 'Share Analytics Data', desc: 'Help improve the platform by sharing usage data' }
                ].map((item) => (
                  <div key={item.key} className={`p-4 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          {item.label}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                          {item.desc}
                        </div>
                      </div>
                      <button
                        onClick={() => setPrivacy(prev => ({ ...prev, [item.key]: !prev[item.key] }))}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ${
                          privacy[item.key]
                            ? darkMode ? 'bg-blue-500' : 'bg-[#00001a]'
                            : darkMode ? 'bg-white/20' : 'bg-gray-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ${
                            privacy[item.key] ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Policies Section */}
        {activeSection === 'policies' && (
          <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl ${
            darkMode
              ? 'bg-white/3 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
              : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-[0_0_25px_rgba(0,0,0,0.15)]'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {}}>
            <div className="flex items-center gap-3 mb-6">
              <div className={`p-3 rounded-lg ${
                darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
              }`}>
                <FileText size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
              </div>
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Policies & Legal
                </h2>
                <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Review platform policies and legal documents
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                {
                  key: 'subscription',
                  label: 'Subscription Policy',
                  desc: 'Terms and conditions for subscriptions',
                  icon: CreditCard,
                  color: 'blue'
                },
                {
                  key: 'report',
                  label: 'Reporting Policy',
                  desc: 'Guidelines for reporting content and users',
                  icon: AlertTriangle,
                  color: 'orange'
                },
                {
                  key: 'refer',
                  label: 'Referral Policy',
                  desc: 'Terms for referral program and rewards',
                  icon: Users,
                  color: 'green'
                },
                {
                  key: 'payment',
                  label: 'Payment Policy',
                  desc: 'Payment terms, refunds, and billing',
                  icon: DollarSign,
                  color: 'purple'
                }
              ].map((policy) => {
                const Icon = policy.icon
                return (
                  <div
                    key={policy.key}
                    onClick={() => handlePolicyClick(policy.label)}
                    className={`group p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                        : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg ${
                        policy.color === 'blue' ? (darkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-[#00001a]/10 text-[#00001a]') :
                        policy.color === 'orange' ? (darkMode ? 'bg-orange-500/20 text-orange-400' : 'bg-[#00001a]/10 text-[#00001a]') :
                        policy.color === 'green' ? (darkMode ? 'bg-green-500/20 text-green-400' : 'bg-[#00001a]/10 text-[#00001a]') :
                        (darkMode ? 'bg-purple-500/20 text-purple-400' : 'bg-[#00001a]/10 text-[#00001a]')
                      }`}>
                        <Icon size={20} />
                      </div>
                      <div className="flex-1">
                        <div className={`font-medium mb-1 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          {policy.label}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                          {policy.desc}
                        </div>
                        <div className={`text-xs mt-2 ${darkMode ? 'text-blue-500' : 'text-[#00001a]'} group-hover:underline`}>
                          View Policy →
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Legal Links */}
            <div className={`mt-6 p-4 rounded-lg border transition-all duration-300 ${
              darkMode
                ? 'border-white/10 bg-white/5 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                : 'border-gray-200 bg-gray-50 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
            }`}>
              <div className={`text-sm font-medium mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Legal Documents
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                {[
                  'Terms of Service',
                  'Privacy Policy',
                  'Cookie Policy',
                  'Community Guidelines',
                  'DMCA Policy'
                ].map((doc) => (
                  <button
                    key={doc}
                    onClick={() => handlePolicyClick(doc)}
                    className={`text-left px-3 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'text-blue-400 border-white/20 hover:border-blue-400/50 hover:bg-white/5 hover:shadow-[0_2px_8px_rgba(59,130,246,0.25)]'
                        : 'text-[#00001a] border-gray-200 hover:border-[#00001a]/30 hover:bg-[#00001a]/5 hover:shadow-[0_2px_8px_rgba(0,0,26,0.15)]'
                    }`}
                  >
                    {doc}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* MFA Section */}
        {activeSection === 'mfa' && (
          <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl ${
            darkMode
              ? 'bg-white/3 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
              : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-[0_0_25px_rgba(0,0,0,0.15)]'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {}}>
            <div className="flex items-center gap-3 mb-6">
              <div className={`p-3 rounded-lg ${
                darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
              }`}>
                <Lock size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
              </div>
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Multi-Factor Authentication
                </h2>
                <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Add an extra layer of security to your account
                </p>
              </div>
            </div>

            {/* MFA Status */}
            <div className={`p-4 rounded-lg border mb-6 ${
              mfa.enabled
                ? darkMode
                  ? 'border-green-500/30 bg-green-500/10'
                  : 'border-[#00001a]/30 bg-[#00001a]/5'
                : darkMode
                  ? 'border-orange-500/30 bg-orange-500/10'
                  : 'border-[#00001a]/30 bg-[#00001a]/5'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    mfa.enabled
                      ? darkMode ? 'bg-green-500/20 text-green-400' : 'bg-[#00001a]/10 text-[#00001a]'
                      : darkMode ? 'bg-orange-500/20 text-orange-400' : 'bg-[#00001a]/10 text-[#00001a]'
                  }`}>
                    {mfa.enabled ? <Check size={20} /> : <AlertTriangle size={20} />}
                  </div>
                  <div>
                    <div className={`font-medium ${
                      mfa.enabled
                        ? darkMode ? 'text-green-400' : 'text-[#00001a]'
                        : darkMode ? 'text-orange-400' : 'text-[#00001a]'
                    }`}>
                      {mfa.enabled ? 'MFA Enabled' : 'MFA Disabled'}
                    </div>
                    <div className={`text-sm ${
                      mfa.enabled
                        ? darkMode ? 'text-green-400/70' : 'text-[#00001a]/70'
                        : darkMode ? 'text-orange-400/70' : 'text-[#00001a]/70'
                    }`}>
                      {mfa.enabled
                        ? 'Your account is protected with multi-factor authentication'
                        : 'Enable MFA to secure your account'
                      }
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setMfa(prev => ({ ...prev, enabled: !prev.enabled }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ${
                    mfa.enabled
                      ? darkMode ? 'bg-green-500' : 'bg-[#00001a]'
                      : darkMode ? 'bg-white/20' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ${
                      mfa.enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* MFA Configuration */}
            {mfa.enabled && (
              <div className="space-y-6">
                {/* Authentication Method */}
                <div className={`p-4 rounded-lg border transition-all duration-300 ${
                  darkMode
                    ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                }`}>
                  <div className="mb-3">
                    <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Authentication Method
                    </div>
                    <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                      Choose how you want to receive authentication codes
                    </div>
                  </div>
                  <div className="space-y-3">
                    {[
                      { value: 'sms', label: 'SMS Text Message', desc: 'Receive codes via text message', icon: Smartphone },
                      { value: 'email', label: 'Email', desc: 'Receive codes via email', icon: Mail },
                      { value: 'app', label: 'Authenticator App', desc: 'Use Google Authenticator or similar app', icon: Lock }
                    ].map((method) => {
                      const Icon = method.icon
                      return (
                        <label key={method.value} className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-all duration-300 ${
                          mfa.method === method.value
                            ? darkMode
                              ? 'border-blue-500/30 bg-blue-500/10'
                              : 'border-[#00001a] bg-[#00001a]/5'
                            : darkMode
                              ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                              : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                        }`}>
                          <input
                            type="radio"
                            name="mfaMethod"
                            value={method.value}
                            checked={mfa.method === method.value}
                            onChange={(e) => setMfa(prev => ({ ...prev, method: e.target.value }))}
                            className={`w-4 h-4 ${darkMode ? 'text-blue-500' : 'text-[#00001a]'}`}
                          />
                          <Icon size={20} className={darkMode ? 'text-white/70' : 'text-gray-600'} />
                          <div>
                            <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              {method.label}
                            </div>
                            <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                              {method.desc}
                            </div>
                          </div>
                        </label>
                      )
                    })}
                  </div>
                </div>

                {/* Backup Codes */}
                <div className={`p-4 rounded-lg border transition-all duration-300 ${
                  darkMode
                    ? 'border-white/10 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-[0_0_15px_rgba(0,0,0,0.1)]'
                }`}>
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <div className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Backup Codes
                      </div>
                      <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                        Generate backup codes for account recovery
                      </div>
                    </div>
                    <button
                      onClick={() => setMfa(prev => ({ ...prev, backupCodes: !prev.backupCodes }))}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ${
                        mfa.backupCodes
                          ? darkMode ? 'bg-blue-500' : 'bg-[#00001a]'
                          : darkMode ? 'bg-white/20' : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ${
                          mfa.backupCodes ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                  {mfa.backupCodes && (
                    <button
                      onClick={handleGenerateBackupCodes}
                      className={`w-full mt-3 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                        darkMode
                          ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                          : 'bg-[#00001a] text-white border border-[#00001a] hover:border-[#00001a]/80 hover:shadow-[0_0_15px_rgba(0,0,26,0.2)]'
                      }`}
                    >
                      Generate New Backup Codes
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Setup Button */}
            {!mfa.enabled && (
              <button
                onClick={() => setMfa(prev => ({ ...prev, enabled: true }))}
                className={`w-full mt-6 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  darkMode
                    ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.4)]'
                    : 'bg-[#00001a] text-white border border-[#00001a] hover:border-[#00001a]/80 hover:shadow-[0_0_15px_rgba(0,0,26,0.2)]'
                }`}
              >
                Set Up Multi-Factor Authentication
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default Settings
