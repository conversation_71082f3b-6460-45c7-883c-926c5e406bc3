import React, { useState } from 'react'
import { ArrowLeft, Upload, Camera, CheckCircle, AlertCircle, Clock, Shield, FileText, User, Check } from 'lucide-react'

const IdentityVerification = ({ darkMode }) => {
  const [currentStep, setCurrentStep] = useState(0) // 0: selection, 1: process, 2: complete
  const [verificationLevel, setVerificationLevel] = useState('')
  const [verificationStatus, setVerificationStatus] = useState({
    light: 'not_started', // not_started, in_progress, completed
    standard: 'not_started',
    full: 'not_started'
  })

  // Progress tracking
  const [currentVerificationLevel, setCurrentVerificationLevel] = useState('light') // light, standard, full
  const [completedLevels, setCompletedLevels] = useState([]) // Array of completed levels

  // Verification levels configuration
  const verificationLevels = [
    { id: 'light', name: 'Welcome', step: 1, description: 'Basic information' },
    { id: 'standard', name: 'ID Upload', step: 2, description: 'Document verification' },
    { id: 'full', name: 'Face Verification', step: 3, description: 'Biometric verification' },
    { id: 'complete', name: 'Complete', step: 4, description: 'Verification complete' }
  ]

  // Helper functions
  const isLevelCompleted = (levelId) => completedLevels.includes(levelId)
  const isLevelAccessible = (levelId) => {
    const levelIndex = verificationLevels.findIndex(level => level.id === levelId)
    if (levelIndex === 0) return true // First level is always accessible
    const previousLevel = verificationLevels[levelIndex - 1]
    return previousLevel ? isLevelCompleted(previousLevel.id) : false
  }
  const getCurrentStepNumber = () => {
    const currentLevel = verificationLevels.find(level => level.id === currentVerificationLevel)
    return currentLevel ? currentLevel.step : 1
  }

  // Level completion handlers
  const completeLevel = (levelId) => {
    if (!completedLevels.includes(levelId)) {
      setCompletedLevels(prev => {
        const newCompletedLevels = [...prev, levelId]

        // Check if all three main verification levels are completed
        const mainLevels = ['light', 'standard', 'full']
        const allMainLevelsCompleted = mainLevels.every(level =>
          newCompletedLevels.includes(level) || level === levelId
        )

        // If all main levels are completed, also mark 'complete' as completed
        if (allMainLevelsCompleted && !newCompletedLevels.includes('complete')) {
          return [...newCompletedLevels, 'complete']
        }

        return newCompletedLevels
      })
    }

    // Auto-progress to next level
    const currentIndex = verificationLevels.findIndex(level => level.id === levelId)
    const nextLevel = verificationLevels[currentIndex + 1]
    if (nextLevel && nextLevel.id !== 'complete') {
      setCurrentVerificationLevel(nextLevel.id)
    } else if (nextLevel && nextLevel.id === 'complete') {
      setCurrentVerificationLevel('complete')
    }
  }

  const startVerification = (levelId) => {
    if (isLevelAccessible(levelId)) {
      setCurrentVerificationLevel(levelId)
      setCurrentStep(1)
      setVerificationLevel(levelId)
    }
  }

  // Progress Indicator Component
  const ProgressIndicator = () => (
    <div className="mb-8">
      <div className="text-center mb-6">
        <h1 className={`text-3xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
          KYC Verification
        </h1>
        <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
          Complete the steps below to complete your KYC verification
        </p>
      </div>

      <div className="flex items-center justify-between max-w-4xl mx-auto relative">
        {verificationLevels.map((level, index) => {
          const isCompleted = isLevelCompleted(level.id)
          const isCurrent = currentVerificationLevel === level.id
          const isAccessible = isLevelAccessible(level.id)

          return (
            <div key={level.id} className="flex flex-col items-center relative z-10">
              {/* Step Circle */}
              <div className={`w-14 h-14 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-500 shadow-lg ${
                isCompleted
                  ? darkMode
                    ? 'bg-blue-500 text-white border-2 border-blue-500'
                    : 'bg-[#00001a] text-white border-2 border-[#00001a]'
                  : isCurrent
                    ? darkMode
                      ? 'bg-blue-500 text-white border-2 border-blue-500'
                      : 'bg-[#00001a] text-white border-2 border-[#00001a]'
                    : isAccessible
                      ? darkMode
                        ? 'bg-gray-600 text-gray-300 border-2 border-gray-600'
                        : 'bg-gray-200 text-gray-500 border-2 border-gray-200'
                      : darkMode
                        ? 'bg-gray-700 text-gray-500 border-2 border-gray-700'
                        : 'bg-gray-100 text-gray-400 border-2 border-gray-100'
              }`}>
                {isCompleted ? <Check size={24} /> : level.step}
              </div>

              {/* Step Labels */}
              <div className="mt-4 text-center max-w-[140px]">
                <div className={`text-base font-bold mb-1 ${
                  isCurrent || isCompleted
                    ? darkMode ? 'text-white' : 'text-[#00001a]'
                    : darkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {level.name}
                </div>
                <div className={`text-sm ${
                  isCurrent || isCompleted
                    ? darkMode ? 'text-gray-300' : 'text-[#00001a]/70'
                    : darkMode ? 'text-gray-500' : 'text-gray-500'
                }`}>
                  {level.description}
                </div>
              </div>
            </div>
          )
        })}

        {/* Connection Lines - Positioned to connect circle edges */}
        <div className="absolute top-7 z-0" style={{ left: '56px', right: '56px' }}>
          <div className="flex items-center justify-between h-1">
            {verificationLevels.slice(0, -1).map((level, index) => {
              const isCompleted = isLevelCompleted(level.id)
              const nextIsCompleted = isLevelCompleted(verificationLevels[index + 1].id)
              const isCurrent = currentVerificationLevel === level.id

              return (
                <div
                  key={`line-${index}`}
                  className={`flex-1 h-1 transition-all duration-500 ${
                    isCompleted || nextIsCompleted || isCurrent
                      ? darkMode
                        ? 'bg-blue-500'
                        : 'bg-[#00001a]'
                      : darkMode
                        ? 'bg-gray-600'
                        : 'bg-gray-300'
                  }`}
                />
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
  const [verificationData, setVerificationData] = useState({
    email: { verified: false, value: '' },
    phone: { verified: false, value: '' },
    id: { uploaded: false, verified: false, fileName: '', file: null },
    face: { completed: false, verified: false, inProgress: false, failed: false, step: 'idle' }
  })
  const [currentProcessStep, setCurrentProcessStep] = useState(0) // For multi-step processes



  const handleBackToSelection = () => {
    setCurrentStep(0)
    setVerificationLevel('')
  }

  return (
    <div className={`h-full w-full transition-all duration-500 ${
      darkMode ? 'bg-[#00001a]' : 'bg-white'
    }`}>
      {/* Header */}
      <div className="p-6 pb-0">
        {/* Progress Indicator */}
        <ProgressIndicator />

        <div className="flex items-center justify-between mb-6">
          {currentStep > 0 && (
            <button
              onClick={handleBackToSelection}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 backdrop-blur-xl border ${
                darkMode
                  ? 'bg-white/10 text-white hover:bg-white/20 border-white/20'
                  : 'bg-[#00001a]/10 text-[#00001a] hover:bg-[#00001a]/20 border-[#00001a]/20'
              }`}
            >
              ← Back to Selection
            </button>
          )}
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Step 0: Verification Level Selection */}
        {currentStep === 0 && (
          <>
          <div>
            <p className={`text-lg mb-8 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Choose your verification level to get started
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Light Verification */}
              <div
                onClick={() => startVerification('light')}
                className={`group p-6 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden ${
                  isLevelCompleted('light')
                    ? 'cursor-default'
                    : isLevelAccessible('light')
                      ? 'cursor-pointer'
                      : 'cursor-not-allowed opacity-50'
                } ${
                  isLevelCompleted('light')
                    ? darkMode
                      ? 'rounded-lg bg-transparent border-white/30'
                      : 'rounded-lg bg-white border-[#00001a]/30'
                    : currentVerificationLevel === 'light'
                      ? darkMode
                        ? 'rounded-lg bg-transparent border-white/30'
                        : 'rounded-lg bg-white border-[#00001a]/30'
                      : darkMode
                        ? 'rounded-lg bg-transparent border-white/20 hover:bg-transparent hover:border-white/30'
                        : 'rounded-lg bg-white border-[#00001a]/20 hover:bg-white hover:border-[#00001a]/30'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 26, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
              >
                <div className="mb-4 flex items-center justify-between">
                  <h3 className={`text-lg font-semibold ${
                    isLevelCompleted('light')
                      ? 'text-[#00001a]'
                      : currentVerificationLevel === 'light'
                        ? darkMode ? 'text-white' : 'text-[#00001a]'
                        : darkMode ? 'text-white' : 'text-[#00001a]'
                  }`}>
                    Light Verification
                  </h3>
                  {isLevelCompleted('light') && (
                    <CheckCircle className={darkMode ? "text-white" : "text-[#00001a]"} size={20} />
                  )}
                </div>
                <p className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Email and phone verification only
                </p>
                <ul className={`text-sm space-y-2 mb-4 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  <li>• Basic account access</li>
                  <li>• Limited transaction amounts</li>
                  <li>• Quick setup process</li>
                </ul>


              </div>

              {/* Standard Verification */}
              <div
                onClick={() => isLevelAccessible('standard') ? startVerification('standard') : null}
                className={`group p-6 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden ${
                  isLevelCompleted('standard')
                    ? 'cursor-default'
                    : isLevelAccessible('standard')
                      ? 'cursor-pointer'
                      : 'cursor-not-allowed opacity-50'
                } ${
                  isLevelCompleted('standard')
                    ? darkMode
                      ? 'rounded-lg bg-transparent border-white/30'
                      : 'rounded-lg bg-white border-[#00001a]/30'
                    : currentVerificationLevel === 'standard'
                      ? darkMode
                        ? 'rounded-lg bg-transparent border-white/30'
                        : 'rounded-lg bg-white border-[#00001a]/30'
                      : isLevelAccessible('standard')
                        ? darkMode
                          ? 'rounded-lg bg-transparent border-white/20 hover:bg-transparent hover:border-white/30'
                          : 'rounded-lg bg-white border-[#00001a]/20 hover:bg-white hover:border-[#00001a]/30'
                        : darkMode
                          ? 'rounded-lg bg-transparent border-white/10'
                          : 'rounded-lg bg-white border-[#00001a]/20'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 26, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
              >
                <div className="mb-4 flex items-center justify-between">
                  <h3 className={`text-lg font-semibold ${
                    isLevelCompleted('standard')
                      ? 'text-[#00001a]'
                      : currentVerificationLevel === 'standard'
                        ? darkMode ? 'text-white' : 'text-[#00001a]'
                        : isLevelAccessible('standard')
                          ? darkMode ? 'text-white' : 'text-[#00001a]'
                          : darkMode ? 'text-white/40' : 'text-[#00001a]/40'
                  }`}>
                    Standard Verification
                  </h3>
                  {isLevelCompleted('standard') && (
                    <CheckCircle className={darkMode ? "text-white" : "text-[#00001a]"} size={20} />
                  )}
                  {!isLevelAccessible('standard') && (
                    <div className={`text-xs px-2 py-1 rounded ${
                      darkMode ? 'bg-white/10 text-white/60' : 'bg-[#00001a]/10 text-[#00001a]/60'
                    }`}>
                      Complete Light first
                    </div>
                  )}
                </div>
                <p className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Upload government-issued ID
                </p>
                <ul className={`text-sm space-y-2 mb-4 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  <li>• Higher transaction limits</li>
                  <li>• Access to premium features</li>
                  <li>• Enhanced security</li>
                </ul>


              </div>

              {/* Full Verification */}
              <div
                onClick={() => isLevelAccessible('full') ? startVerification('full') : null}
                className={`group p-6 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden ${
                  isLevelCompleted('full')
                    ? 'cursor-default'
                    : isLevelAccessible('full')
                      ? 'cursor-pointer'
                      : 'cursor-not-allowed opacity-50'
                } ${
                  isLevelCompleted('full')
                    ? darkMode
                      ? 'rounded-lg bg-transparent border-white/30'
                      : 'rounded-lg bg-white border-[#00001a]/30'
                    : currentVerificationLevel === 'full'
                      ? darkMode
                        ? 'rounded-lg bg-transparent border-white/30'
                        : 'rounded-lg bg-white border-[#00001a]/30'
                      : isLevelAccessible('full')
                        ? darkMode
                          ? 'rounded-lg bg-transparent border-white/20 hover:bg-transparent hover:border-white/30'
                          : 'rounded-lg bg-white border-[#00001a]/20 hover:bg-white hover:border-[#00001a]/30'
                        : darkMode
                          ? 'rounded-lg bg-transparent border-white/10'
                          : 'rounded-lg bg-white border-[#00001a]/20'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 26, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 26, 0.15)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 26, 0.1)';
                  }
                }}
              >
                <div className="mb-4 flex items-center justify-between">
                  <h3 className={`text-lg font-semibold ${
                    isLevelCompleted('full')
                      ? 'text-[#00001a]'
                      : currentVerificationLevel === 'full'
                        ? darkMode ? 'text-white' : 'text-[#00001a]'
                        : isLevelAccessible('full')
                          ? darkMode ? 'text-white' : 'text-[#00001a]'
                          : darkMode ? 'text-white/40' : 'text-[#00001a]/40'
                  }`}>
                    Full Verification
                  </h3>
                  {isLevelCompleted('full') && (
                    <CheckCircle className={darkMode ? "text-white" : "text-[#00001a]"} size={20} />
                  )}
                  {!isLevelAccessible('full') && (
                    <div className={`text-xs px-2 py-1 rounded ${
                      darkMode ? 'bg-white/10 text-white/60' : 'bg-[#00001a]/10 text-[#00001a]/60'
                    }`}>
                      Complete Standard first
                    </div>
                  )}
                </div>
                <p className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  ID plus dynamic facial authentication
                </p>
                <ul className={`text-sm space-y-2 mb-4 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  <li>• Unlimited transactions</li>
                  <li>• All platform features</li>
                  <li>• Maximum security level</li>
                </ul>


              </div>
            </div>
          </div>

          {/* Verification Status Section */}
          <div
            className={`backdrop-blur-xl border p-8 transition-all duration-500 shadow-xl ${
              darkMode
                ? 'bg-[#00001a]/40 border-white/10 rounded-lg'
                : 'bg-white/40 border-white/20 rounded-lg'
            }`}
            style={darkMode ? {
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
              transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
            } : {
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              transition: 'all 0.3s ease'
            }}
          >
            <div className="text-center mb-10">
              <div className={`group w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center transition-all duration-500 cursor-pointer backdrop-blur-xl shadow-xl relative overflow-hidden ${
                darkMode ? 'bg-[#00001a]/40 border border-white/10 hover:bg-[#00001a]/50 hover:border-white/20' : 'bg-white/40 border border-white/20'
              }`}
              style={darkMode ? {
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
              } : {
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                } else {
                  e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                }
              }}
              onMouseLeave={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                } else {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                }
              }}
              >
                <svg className={`w-10 h-10 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h2 className={`text-3xl font-bold mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Verification Status
              </h2>
              <p className={`text-lg max-w-2xl mx-auto ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                Track your verification progress and processing timeline
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {/* Light Verification Status */}
              <div
                className={`group p-6 border transition-all duration-500 cursor-pointer backdrop-blur-xl shadow-xl relative overflow-hidden ${
                  darkMode
                    ? 'bg-[#00001a]/40 border-white/10 rounded-lg hover:bg-[#00001a]/50 hover:border-white/20'
                    : 'bg-white/40 border-white/20 rounded-lg'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className={`w-4 h-4 rounded-full flex-shrink-0 ${
                    verificationStatus.light === 'completed'
                      ? darkMode ? 'bg-green-500' : 'bg-[#00001a]'
                      : verificationStatus.light === 'in_progress'
                        ? darkMode ? 'bg-yellow-500' : 'bg-[#00001a]/60'
                        : darkMode ? 'bg-white/30' : 'bg-[#00001a]/30'
                  }`}></div>
                  <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Light Verification
                  </h3>
                </div>

                <div className="space-y-4">
                  <div className={`text-base font-semibold ${
                    verificationStatus.light === 'completed'
                      ? darkMode ? 'text-green-500' : 'text-[#00001a]'
                      : verificationStatus.light === 'in_progress' ? 'text-yellow-500' :
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`}>
                    {verificationStatus.light === 'completed' ? 'Verification Complete' :
                     verificationStatus.light === 'in_progress' ? 'Processing...' :
                     'Not Started'}
                  </div>

                  <div className={`text-sm ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                    {verificationStatus.light === 'completed' ? 'Completed instantly' :
                     verificationStatus.light === 'in_progress' ? 'Processing time: Instant' :
                     'Processing time: Instant'}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationData.email.verified
                          ? darkMode ? 'bg-green-500' : 'bg-[#00001a]'
                          : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationData.email.verified
                          ? darkMode ? 'text-green-500' : 'text-[#00001a]'
                          : darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        Email Verification
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationData.phone.verified
                          ? darkMode ? 'bg-green-500' : 'bg-[#00001a]'
                          : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationData.phone.verified
                          ? darkMode ? 'text-green-500' : 'text-[#00001a]'
                          : darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        Phone Verification
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Standard Verification Status */}
              <div
                className={`group p-6 border transition-all duration-500 cursor-pointer backdrop-blur-xl shadow-xl relative overflow-hidden ${
                  darkMode
                    ? 'bg-[#00001a]/40 border-white/10 rounded-lg hover:bg-[#00001a]/50 hover:border-white/20'
                    : 'bg-white/40 border-white/20 rounded-lg'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className={`w-4 h-4 rounded-full flex-shrink-0 ${
                    verificationStatus.standard === 'completed'
                      ? darkMode ? 'bg-green-500' : 'bg-[#00001a]'
                      : verificationStatus.standard === 'in_progress'
                        ? darkMode ? 'bg-yellow-500' : 'bg-[#00001a]/60'
                        : darkMode ? 'bg-white/30' : 'bg-[#00001a]/30'
                  }`}></div>
                  <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Standard Verification
                  </h3>
                </div>

                <div className="space-y-4">
                  <div className={`text-base font-semibold ${
                    verificationStatus.standard === 'completed'
                      ? darkMode ? 'text-green-500' : 'text-[#00001a]'
                      : verificationStatus.standard === 'in_progress' ? 'text-yellow-500' :
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`}>
                    {verificationStatus.standard === 'completed' ? 'Verification Complete' :
                     verificationStatus.standard === 'in_progress' ? 'Under Review' :
                     'Not Started'}
                  </div>

                  <div className={`text-sm ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                    {verificationStatus.standard === 'completed' ? 'Approved in 2 business days' :
                     verificationStatus.standard === 'in_progress' ? 'Processing time: 2-3 business days' :
                     'Processing time: 2-3 business days'}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationData.id.uploaded ? 'bg-[#00001a]' : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationData.id.uploaded ? 'text-[#00001a]' : darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        ID Document Upload
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationStatus.standard === 'completed' ? 'bg-[#00001a]' :
                        verificationStatus.standard === 'in_progress' ? 'bg-yellow-500' : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationStatus.standard === 'completed' ? 'text-[#00001a]' :
                        verificationStatus.standard === 'in_progress' ? 'text-yellow-500' :
                        darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        Document Review
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Full Verification Status */}
              <div
                className={`group p-6 border transition-all duration-500 cursor-pointer backdrop-blur-xl shadow-xl relative overflow-hidden ${
                  darkMode
                    ? 'bg-[#00001a]/40 border-white/10 rounded-lg hover:bg-[#00001a]/50 hover:border-white/20'
                    : 'bg-white/40 border-white/20 rounded-lg'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className={`w-4 h-4 rounded-full flex-shrink-0 ${
                    verificationStatus.full === 'completed'
                      ? darkMode ? 'bg-green-500' : 'bg-[#00001a]'
                      : verificationStatus.full === 'in_progress'
                        ? darkMode ? 'bg-yellow-500' : 'bg-[#00001a]/60'
                        : darkMode ? 'bg-white/30' : 'bg-[#00001a]/30'
                  }`}></div>
                  <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Full Verification
                  </h3>
                </div>

                <div className="space-y-4">
                  <div className={`text-base font-semibold ${
                    verificationStatus.full === 'completed' ? 'text-[#00001a]' :
                    verificationStatus.full === 'in_progress' ? 'text-yellow-500' :
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`}>
                    {verificationStatus.full === 'completed' ? 'Verification Complete' :
                     verificationStatus.full === 'in_progress' ? 'Under Review' :
                     'Not Started'}
                  </div>

                  <div className={`text-sm ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                    {verificationStatus.full === 'completed' ? 'Approved in 3 business days' :
                     verificationStatus.full === 'in_progress' ? 'Processing time: 3-5 business days' :
                     'Processing time: 3-5 business days'}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationData.id.uploaded ? 'bg-[#00001a]' : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationData.id.uploaded ? 'text-[#00001a]' : darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        ID Document Upload
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationData.face.completed ? 'bg-[#00001a]' : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationData.face.completed ? 'text-[#00001a]' : darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        Facial Authentication
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        verificationStatus.full === 'completed' ? 'bg-[#00001a]' :
                        verificationStatus.full === 'in_progress' ? 'bg-yellow-500' : 'bg-gray-400'
                      }`}></div>
                      <span className={`text-sm ${
                        verificationStatus.full === 'completed' ? 'text-[#00001a]' :
                        verificationStatus.full === 'in_progress' ? 'text-yellow-500' :
                        darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                      }`}>
                        Identity Verification
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
          </>
        )}

        {/* Step 1: Verification Process */}
        {currentStep === 1 && (
          <div className={`backdrop-blur-xl border p-8 transition-all duration-500 ${
            darkMode
              ? 'bg-white/5 border-white/20 rounded-lg'
              : 'bg-white border-[#00001a]/20 rounded-lg'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transition: 'all 0.3s ease'
          }}>

            {/* Light Verification Process */}
            {verificationLevel === 'light' && (
              <div>
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
                  }`}>
                    <svg className={`w-8 h-8 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Light Verification
                  </h2>
                  <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Verify your email and phone number
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Email Verification */}
                  <div className={`p-6 rounded-lg border ${
                    darkMode ? 'bg-white/5 border-white/10' : 'bg-white border-[#00001a]/20'
                  }`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Email Verification
                      </h3>
                      {verificationData.email.verified && (
                        <div className={`flex items-center space-x-2 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm font-medium">Verified</span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-4">
                      <input
                        type="email"
                        placeholder="Enter your email address"
                        value={verificationData.email.value}
                        onChange={(e) => setVerificationData(prev => ({
                          ...prev,
                          email: { ...prev.email, value: e.target.value }
                        }))}
                        className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40'
                            : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/40'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                      />
                      <button
                        onClick={() => {
                          if (verificationData.email.value) {
                            setVerificationData(prev => ({
                              ...prev,
                              email: { ...prev.email, verified: true }
                            }))
                          }
                        }}
                        disabled={!verificationData.email.value || verificationData.email.verified}
                        className={`w-full px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
                          verificationData.email.verified
                            ? darkMode
                              ? 'bg-green-500/20 text-green-400 cursor-not-allowed'
                              : 'bg-[#00001a]/10 text-[#00001a] cursor-not-allowed'
                            : !verificationData.email.value
                              ? darkMode
                                ? 'bg-white/10 text-white/50 cursor-not-allowed'
                                : 'bg-[#00001a]/10 text-[#00001a]/50 cursor-not-allowed'
                              : darkMode
                                ? 'bg-white text-[#00001a] hover:bg-white/90'
                                : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                        }`}
                      >
                        {verificationData.email.verified ? 'Email Verified' : 'Send Verification Code'}
                      </button>
                    </div>
                  </div>

                  {/* Phone Verification */}
                  <div className={`p-6 rounded-lg border ${
                    darkMode ? 'bg-white/5 border-white/10' : 'bg-white border-[#00001a]/20'
                  }`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Phone Verification
                      </h3>
                      {verificationData.phone.verified && (
                        <div className={`flex items-center space-x-2 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm font-medium">Verified</span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-4">
                      <input
                        type="tel"
                        placeholder="Enter your phone number"
                        value={verificationData.phone.value}
                        onChange={(e) => setVerificationData(prev => ({
                          ...prev,
                          phone: { ...prev.phone, value: e.target.value }
                        }))}
                        className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40'
                            : 'bg-white border-gray-300 text-[#00001a] placeholder-gray-500 focus:border-[#00001a]/40'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                      />
                      <button
                        onClick={() => {
                          if (verificationData.phone.value) {
                            setVerificationData(prev => ({
                              ...prev,
                              phone: { ...prev.phone, verified: true }
                            }))
                          }
                        }}
                        disabled={!verificationData.phone.value || verificationData.phone.verified}
                        className={`w-full px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
                          verificationData.phone.verified
                            ? darkMode
                              ? 'bg-green-500/20 text-green-400 cursor-not-allowed'
                              : 'bg-white text-[#00001a] cursor-not-allowed border border-[#00001a]/20'
                            : !verificationData.phone.value
                              ? darkMode
                                ? 'bg-white/10 text-white/50 cursor-not-allowed'
                                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                              : darkMode
                                ? 'bg-white text-[#00001a] hover:bg-white/90'
                                : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                        }`}
                      >
                        {verificationData.phone.verified ? 'Phone Verified' : 'Send SMS Code'}
                      </button>
                    </div>
                  </div>

                  {/* Complete Button */}
                  <div className="flex justify-center pt-4">
                    <button
                      onClick={() => {
                        if (verificationData.email.verified && verificationData.phone.verified) {
                          setVerificationStatus(prev => ({
                            ...prev,
                            light: 'completed'
                          }))
                          completeLevel('light')
                          setCurrentStep(0) // Return to selection screen
                        }
                      }}
                      disabled={!verificationData.email.verified || !verificationData.phone.verified}
                      className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                        verificationData.email.verified && verificationData.phone.verified
                          ? darkMode
                            ? 'bg-white text-[#00001a] hover:bg-white/90'
                            : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                          : darkMode
                            ? 'bg-white/10 text-white/50 cursor-not-allowed'
                            : 'bg-[#00001a]/20 text-[#00001a]/50 cursor-not-allowed'
                      }`}
                      style={verificationData.email.verified && verificationData.phone.verified ? (darkMode ? {
                        boxShadow: '0 4px 12px rgba(255, 255, 255, 0.2)'
                      } : {
                        boxShadow: '0 4px 12px rgba(0, 0, 26, 0.3)'
                      }) : {}}
                    >
                      Complete Light Verification
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Standard Verification Process */}
            {verificationLevel === 'standard' && (
              <div>
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
                  }`}>
                    <svg className={`w-8 h-8 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Standard Verification
                  </h2>
                  <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Upload your government-issued ID document
                  </p>
                </div>

                <div className="space-y-6">
                  {/* ID Upload Section */}
                  <div className={`p-6 rounded-lg border ${
                    darkMode ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Government ID Document
                      </h3>
                      {verificationData.id.uploaded && (
                        <div className={`flex items-center space-x-2 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm font-medium">Uploaded</span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        Please upload a clear photo of your government-issued ID (passport, driver's license, or national ID card).
                      </p>

                      {!verificationData.id.uploaded ? (
                        <div className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
                          darkMode
                            ? 'border-white/20 hover:border-white/40 bg-white/5'
                            : 'border-gray-300 hover:border-gray-400 bg-gray-50'
                        }`}>
                          <svg className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-white/40' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            Upload ID Document
                          </p>
                          <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                            Click to browse or drag and drop your file here
                          </p>
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => {
                              const file = e.target.files[0]
                              if (file) {
                                setVerificationData(prev => ({
                                  ...prev,
                                  id: {
                                    ...prev.id,
                                    uploaded: true,
                                    fileName: file.name,
                                    file: file
                                  }
                                }))
                              }
                            }}
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                          />
                        </div>
                      ) : (
                        <div className={`p-4 rounded-lg border ${
                          darkMode ? 'bg-green-500/10 border-green-500/20' : 'bg-white border-[#00001a]/20'
                        }`}>
                          <div className="flex items-center space-x-3">
                            <svg className={`w-8 h-8 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div className="flex-1">
                              <p className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                {verificationData.id.fileName}
                              </p>
                              <p className={`text-sm ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                                Document uploaded successfully
                              </p>
                            </div>
                            <button
                              onClick={() => setVerificationData(prev => ({
                                ...prev,
                                id: { uploaded: false, verified: false, fileName: '', file: null }
                              }))}
                              className={`px-3 py-1 text-sm rounded transition-all duration-300 ${
                                darkMode
                                  ? 'bg-white/10 text-white hover:bg-white/20'
                                  : 'bg-gray-200 text-[#00001a] hover:bg-gray-300'
                              }`}
                            >
                              Replace
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Complete Button */}
                  <div className="flex justify-center pt-4">
                    <button
                      onClick={() => {
                        if (verificationData.id.uploaded) {
                          setVerificationStatus(prev => ({
                            ...prev,
                            standard: 'completed'
                          }))
                          completeLevel('standard')
                          setCurrentStep(0) // Return to selection screen
                        }
                      }}
                      disabled={!verificationData.id.uploaded}
                      className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                        verificationData.id.uploaded
                          ? darkMode
                            ? 'bg-white text-[#00001a] hover:bg-white/90'
                            : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                          : darkMode
                            ? 'bg-white/10 text-white/50 cursor-not-allowed'
                            : 'bg-[#00001a]/20 text-[#00001a]/50 cursor-not-allowed'
                      }`}
                      style={verificationData.id.uploaded ? (darkMode ? {
                        boxShadow: '0 4px 12px rgba(255, 255, 255, 0.2)'
                      } : {
                        boxShadow: '0 4px 12px rgba(0, 0, 26, 0.3)'
                      }) : {}}
                    >
                      Complete Standard Verification
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Full Verification Process */}
            {verificationLevel === 'full' && (
              <div>
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
                  }`}>
                    <svg className={`w-8 h-8 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Full Verification
                  </h2>
                  <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Upload ID document and complete facial authentication
                  </p>
                </div>

                <div className="space-y-6">
                  {/* ID Upload Section */}
                  <div className={`p-6 rounded-lg border ${
                    darkMode ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Government ID Document
                      </h3>
                      {verificationData.id.uploaded && (
                        <div className={`flex items-center space-x-2 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm font-medium">Uploaded</span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      {!verificationData.id.uploaded ? (
                        <div className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
                          darkMode
                            ? 'border-white/20 hover:border-white/40 bg-white/5'
                            : 'border-gray-300 hover:border-gray-400 bg-gray-50'
                        }`}>
                          <svg className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-white/40' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            Upload ID Document
                          </p>
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => {
                              const file = e.target.files[0]
                              if (file) {
                                setVerificationData(prev => ({
                                  ...prev,
                                  id: {
                                    ...prev.id,
                                    uploaded: true,
                                    fileName: file.name,
                                    file: file
                                  }
                                }))
                              }
                            }}
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                          />
                        </div>
                      ) : (
                        <div className={`p-4 rounded-lg border ${
                          darkMode ? 'bg-green-500/10 border-green-500/20' : 'bg-white border-[#00001a]/20'
                        }`}>
                          <div className="flex items-center space-x-3">
                            <svg className={`w-8 h-8 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div className="flex-1">
                              <p className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                {verificationData.id.fileName}
                              </p>
                              <p className={`text-sm ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                                Document uploaded successfully
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Facial Authentication Section */}
                  <div className={`p-6 rounded-lg border ${
                    darkMode ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Facial Authentication
                      </h3>
                      {verificationData.face.completed && (
                        <div className={`flex items-center space-x-2 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm font-medium">Completed</span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        Complete facial authentication to verify your identity matches your ID document.
                      </p>

                      {!verificationData.face.completed ? (
                        <div className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
                          verificationData.face.failed
                            ? darkMode
                              ? 'border-red-400/50 bg-red-500/10'
                              : 'border-red-500/50 bg-red-50'
                            : verificationData.face.inProgress
                              ? darkMode
                                ? 'border-blue-400/50 bg-blue-500/10'
                                : 'border-[#00001a]/50 bg-[#00001a]/10'
                              : darkMode
                                ? 'border-white/20 hover:border-white/40 bg-white/5'
                                : 'border-gray-300 hover:border-gray-400 bg-gray-50'
                        }`}>
                          {verificationData.face.failed ? (
                            <div className="flex flex-col items-center">
                              <svg className={`w-16 h-16 mx-auto mb-4 ${darkMode ? 'text-red-400' : 'text-red-500'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                              </svg>
                              <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-red-400' : 'text-red-600'}`}>
                                Verification Failed
                              </p>
                              <p className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                                Please ensure you're looking directly at the camera and try again
                              </p>
                              <button
                                onClick={() => {
                                  setVerificationData(prev => ({
                                    ...prev,
                                    face: { ...prev.face, failed: false, step: 'idle' }
                                  }))
                                }}
                                className={`px-6 py-2 rounded-lg font-medium transition-all duration-300 ${
                                  darkMode
                                    ? 'bg-white text-[#00001a] hover:bg-white/90'
                                    : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                                }`}
                              >
                                Try Again
                              </button>
                            </div>
                          ) : verificationData.face.inProgress ? (
                            <div className="flex flex-col items-center">
                              {verificationData.face.step === 'camera_starting' && (
                                <>
                                  <div className={`w-16 h-16 rounded-full border-4 border-dashed animate-spin mb-4 ${
                                    darkMode ? 'border-blue-400' : 'border-[#00001a]'
                                  }`}></div>
                                  <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                    Starting Camera...
                                  </p>
                                  <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                                    Please wait while we initialize the camera
                                  </p>
                                </>
                              )}
                              {verificationData.face.step === 'look_at_camera' && (
                                <>
                                  <div className={`w-20 h-20 rounded-full border-4 animate-pulse mb-4 flex items-center justify-center ${
                                    darkMode ? 'border-blue-400 bg-blue-500/20' : 'border-[#00001a] bg-[#00001a]/20'
                                  }`}>
                                    <svg className={`w-8 h-8 ${darkMode ? 'text-blue-400' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                  </div>
                                  <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                    Look at the Camera
                                  </p>
                                  <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                                    Please look directly at the camera and keep your face centered
                                  </p>
                                </>
                              )}
                              {verificationData.face.step === 'processing' && (
                                <>
                                  <div className={`w-16 h-16 rounded-full border-4 border-dashed animate-spin mb-4 ${
                                    darkMode ? 'border-blue-400' : 'border-[#00001a]'
                                  }`}></div>
                                  <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                    Processing...
                                  </p>
                                  <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                                    Verifying your identity, please wait
                                  </p>
                                </>
                              )}
                            </div>
                          ) : (
                            <>
                              <svg className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-white/40' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                              <p className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                Start Facial Authentication
                              </p>
                            </>
                          )}
                          <div className="flex justify-center">
                            <button
                            onClick={() => {
                              if (!verificationData.face.inProgress && !verificationData.face.completed && !verificationData.face.failed) {
                                // Start the facial authentication process
                                setVerificationData(prev => ({
                                  ...prev,
                                  face: { ...prev.face, inProgress: true, step: 'camera_starting' }
                                }))

                                // Step 1: Camera starting (1 second)
                                setTimeout(() => {
                                  setVerificationData(prev => ({
                                    ...prev,
                                    face: { ...prev.face, step: 'look_at_camera' }
                                  }))
                                }, 1000)

                                // Step 2: Look at camera instruction (3 seconds)
                                setTimeout(() => {
                                  setVerificationData(prev => ({
                                    ...prev,
                                    face: { ...prev.face, step: 'processing' }
                                  }))
                                }, 4000)

                                // Step 3: Random success/failure (2 seconds processing)
                                setTimeout(() => {
                                  const isSuccess = Math.random() > 0.3 // 70% success rate
                                  setVerificationData(prev => ({
                                    ...prev,
                                    face: {
                                      ...prev.face,
                                      inProgress: false,
                                      completed: isSuccess,
                                      verified: isSuccess,
                                      failed: !isSuccess,
                                      step: isSuccess ? 'completed' : 'failed'
                                    }
                                  }))
                                }, 6000)
                              }
                            }}
                              disabled={!verificationData.id.uploaded || verificationData.face.inProgress}
                              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 flex items-center gap-2 ${
                                verificationData.id.uploaded && !verificationData.face.inProgress
                                  ? darkMode
                                    ? 'bg-white text-[#00001a] hover:bg-white/90'
                                    : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                                  : darkMode
                                    ? 'bg-white/10 text-white/50 cursor-not-allowed'
                                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                              }`}
                            >
                              {verificationData.face.inProgress && (
                                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                              )}
                              {verificationData.face.inProgress
                                ? 'Processing...'
                                : verificationData.id.uploaded
                                  ? 'Start Camera'
                                  : 'Upload ID First'}
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className={`p-4 rounded-lg border ${
                          darkMode ? 'bg-green-500/10 border-green-500/20' : 'bg-white border-[#00001a]/20'
                        }`}>
                          <div className="flex items-center space-x-3">
                            <svg className={`w-8 h-8 ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <div className="flex-1">
                              <p className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                Facial Authentication Complete
                              </p>
                              <p className={`text-sm ${darkMode ? 'text-green-500' : 'text-[#00001a]'}`}>
                                Identity successfully verified
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Complete Button */}
                  <div className="flex justify-center pt-4">
                    <button
                      onClick={() => {
                        if (verificationData.id.uploaded && verificationData.face.completed) {
                          setVerificationStatus(prev => ({
                            ...prev,
                            full: 'completed'
                          }))
                          completeLevel('full')
                          setCurrentStep(2) // Go to completion screen
                        }
                      }}
                      disabled={!verificationData.id.uploaded || !verificationData.face.completed}
                      className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                        verificationData.id.uploaded && verificationData.face.completed
                          ? darkMode
                            ? 'bg-white text-[#00001a] hover:bg-white/90'
                            : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                          : darkMode
                            ? 'bg-white/10 text-white/50 cursor-not-allowed'
                            : 'bg-[#00001a]/20 text-[#00001a]/50 cursor-not-allowed'
                      }`}
                      style={verificationData.id.uploaded && verificationData.face.completed ? (darkMode ? {
                        boxShadow: '0 4px 12px rgba(255, 255, 255, 0.2)'
                      } : {
                        boxShadow: '0 4px 12px rgba(0, 0, 26, 0.3)'
                      }) : {}}
                    >
                      Complete Full Verification
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Step 2: Completion */}
        {currentStep === 2 && (
          <div className={`backdrop-blur-xl border p-8 text-center transition-all duration-500 ${
            darkMode
              ? 'bg-white/5 border-white/20 rounded-lg'
              : 'bg-white border-gray-200 rounded-lg'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transition: 'all 0.3s ease'
          }}>
            <div className={`w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center ${
              darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
            }`}>
              <svg className={`w-10 h-10 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className={`text-2xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Verification Complete!
            </h2>
            <p className={`text-lg mb-8 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Your {verificationLevel} verification has been completed successfully.
            </p>
            <button
              onClick={() => {
                setCurrentStep(0)
                setVerificationLevel('')
              }}
              className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                darkMode
                  ? 'bg-white text-[#00001a] hover:bg-white/90'
                  : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
              }`}
              style={darkMode ? {
                boxShadow: '0 4px 12px rgba(255, 255, 255, 0.2)'
              } : {
                boxShadow: '0 4px 12px rgba(0, 0, 26, 0.3)'
              }}
            >
              Start New Verification
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default IdentityVerification
