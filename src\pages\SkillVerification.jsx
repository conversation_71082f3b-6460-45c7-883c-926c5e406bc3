import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  FolderOpen,
  Video,
  Users,
  Check,
  Lock,
  Upload,
  FileText,
  Play,
  MessageSquare,
  Clock,
  Camera,
  Smartphone,
  Timer,
  CheckCircle,
  AlertCircle,
  X,
  Maximize,
  Eye,
  EyeOff,
  Link,
  ExternalLink,
  Plus,
  Trash2
} from 'lucide-react'

const SkillVerification = ({ darkMode }) => {
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [step2Status, setStep2Status] = useState('pending') // 'pending', 'submitted', 'approved', 'rejected'
  const [queriesSolved, setQueriesSolved] = useState(0)
  const [isRecording, setIsRecording] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [cameraStatus, setCameraStatus] = useState({ face: false, phone: false })
  const [phoneLink, setPhoneLink] = useState('')
  const [projectLinks, setProjectLinks] = useState([''])
  const [showProjectLinksModal, setShowProjectLinksModal] = useState(false)

  // Fullscreen demo states
  const [showInstructions, setShowInstructions] = useState(false)
  const [isFullscreenDemo, setIsFullscreenDemo] = useState(false)
  const [fullscreenExitWarning, setFullscreenExitWarning] = useState(false)
  const [tabSwitchWarning, setTabSwitchWarning] = useState(false)

  const steps = [
    {
      id: 1,
      title: 'Portfolio Submission',
      subtitle: 'Projects + Explanation (Problem → Solution)',
      icon: FolderOpen,
      description: 'Submit your portfolio with detailed project descriptions and solution walkthroughs.'
    },
    {
      id: 2,
      title: 'Live Demo',
      subtitle: '1–2 mins live dual-camera demo',
      icon: Video,
      description: 'Record a 1–2 minute live demo using laptop camera for face and phone camera for keyboard/screen view.'
    },
    {
      id: 3,
      title: 'Live Validation',
      subtitle: 'Solve exactly 10 seeker queries',
      icon: Users,
      description: 'Once your demo is approved, you must solve exactly 10 seeker queries to complete your validation.'
    }
  ]

  // Fullscreen and tab monitoring
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (isFullscreenDemo && !document.fullscreenElement) {
        setFullscreenExitWarning(true)
        setTimeout(() => setFullscreenExitWarning(false), 5000)
      }
    }

    const handleVisibilityChange = () => {
      if (isFullscreenDemo && document.hidden) {
        setTabSwitchWarning(true)
        setTimeout(() => setTabSwitchWarning(false), 5000)
      }
    }

    const handleKeyDown = (e) => {
      if (isFullscreenDemo) {
        // Disable common shortcuts that could exit fullscreen
        if (e.key === 'F11' || (e.altKey && e.key === 'Tab') || (e.ctrlKey && e.key === 'w')) {
          e.preventDefault()
          setFullscreenExitWarning(true)
          setTimeout(() => setFullscreenExitWarning(false), 3000)
        }
      }
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isFullscreenDemo])

  // Enter fullscreen mode
  const enterFullscreen = async () => {
    try {
      await document.documentElement.requestFullscreen()
      setIsFullscreenDemo(true)
      setShowInstructions(false)
    } catch (error) {
      console.error('Error entering fullscreen:', error)
      alert('Unable to enter fullscreen mode. Please try again.')
    }
  }

  // Exit fullscreen mode
  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen()
      }
      setIsFullscreenDemo(false)
      setIsRecording(false)
      setRecordingTime(0)
      setCameraStatus({ face: false, phone: false })
    } catch (error) {
      console.error('Error exiting fullscreen:', error)
    }
  }

  const startLiveDemo = async () => {
    // Show instructions first
    setShowInstructions(true)
  }

  const startActualDemo = async () => {
    setIsLoading(true)
    try {
      // Generate secure phone link with timestamp and random ID
      const timestamp = Date.now()
      const randomId = Math.random().toString(36).substr(2, 12)
      const linkId = `${timestamp}-${randomId}`
      const generatedLink = `${window.location.origin}/phone-camera/${linkId}`
      setPhoneLink(generatedLink)

      // Show phone link to user
      const copyLink = () => {
        navigator.clipboard.writeText(generatedLink)
        alert('Link copied to clipboard!')
      }

      const linkMessage = `Open this link on your phone:\n${generatedLink}\n\nClick OK to copy the link, then enter fullscreen mode.`
      if (window.confirm(linkMessage)) {
        copyLink()
      }

      // Enter fullscreen mode
      await enterFullscreen()

      // Simulate camera connections
      await new Promise(resolve => setTimeout(resolve, 1500))
      setCameraStatus({ face: true, phone: false })

      // Simulate phone connection (user opens link)
      await new Promise(resolve => setTimeout(resolve, 3000))
      setCameraStatus({ face: true, phone: true })

      // Start recording
      setIsRecording(true)
      setRecordingTime(0)

      // Timer simulation
      const timer = setInterval(() => {
        setRecordingTime(prev => {
          if (prev >= 120) { // 2 minutes
            clearInterval(timer)
            setIsRecording(false)
            setStep2Status('submitted')
            // Exit fullscreen and simulate approval after 5 seconds
            setTimeout(async () => {
              await exitFullscreen()
              setStep2Status('approved')
              setCurrentStep(3)
            }, 5000)
            return 120
          }
          return prev + 1
        })
      }, 1000)

    } catch (error) {
      console.error('Error starting live demo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleStepAction = async (stepId) => {
    setIsLoading(true)

    try {
      if (stepId === 1) {
        // Portfolio Submission - Show project links modal
        setShowProjectLinksModal(true)
      } else if (stepId === 2) {
        // Start Live Demo
        await startLiveDemo()
      } else if (stepId === 3) {
        // Live Validation - Redirect to Dashboard Anonymous Doubt Pool
        navigate('/dashboard', {
          state: {
            activeSection: 'anonymousDoubtPool',
            validationMode: true,
            message: 'Complete 10 queries in the Anonymous Doubt Pool to finish your skill verification.'
          }
        })
      }
    } catch (error) {
      console.error('Error in step action:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const isStepCompleted = (stepId) => {
    if (stepId === 2) {
      return step2Status === 'approved'
    }
    return completedSteps.includes(stepId)
  }

  const isStepActive = (stepId) => {
    if (stepId === 2 && step2Status === 'submitted') return false
    if (stepId === 3 && step2Status !== 'approved') return false
    return stepId === currentStep && !isStepCompleted(stepId)
  }

  const isStepLocked = (stepId) => {
    if (stepId === 2) return !completedSteps.includes(1)
    if (stepId === 3) return step2Status !== 'approved'
    return stepId > currentStep
  }

  // Project Links Functions
  const addProjectLink = () => {
    setProjectLinks(prev => [...prev, ''])
  }

  const removeProjectLink = (index) => {
    setProjectLinks(prev => prev.filter((_, i) => i !== index))
  }

  const updateProjectLink = (index, value) => {
    setProjectLinks(prev => prev.map((link, i) => i === index ? value : link))
  }

  const handleProjectLinksSubmit = () => {
    const validLinks = projectLinks.filter(link => link.trim() !== '')
    if (validLinks.length > 0) {
      console.log('Project links submitted:', validLinks)
      setShowProjectLinksModal(false)
      // You can add additional logic here to save the links
    }
  }

  // Instructions Modal Component
  const InstructionsModal = () => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4">
      <div className={`max-w-2xl w-full max-h-[90vh] rounded-2xl overflow-hidden ${
        darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-gray-200'
      }`}>
        <div className="overflow-y-auto max-h-[90vh] p-8">
        <div className="text-center mb-6">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
            darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
          }`}>
            <Video size={32} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
          </div>
          <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
            Live Demo Instructions
          </h2>
          <p className={`${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
            Please read the guidelines carefully before starting your demo
          </p>
        </div>

        <div className="space-y-4 mb-8">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-white/5' : 'bg-gray-50'}`}>
            <h3 className={`font-semibold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              📹 Demo Requirements:
            </h3>
            <ul className={`space-y-1 text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
              <li>• Duration: 1-2 minutes maximum</li>
              <li>• Use laptop camera for face recording</li>
              <li>• Use phone camera for keyboard/screen view</li>
              <li>• Demonstrate your coding skills live</li>
              <li>• Explain your thought process clearly</li>
            </ul>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-white/5' : 'bg-gray-50'}`}>
            <h3 className={`font-semibold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              🔒 Focused Mode:
            </h3>
            <ul className={`space-y-1 text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
              <li>• Demo will run in fullscreen mode</li>
              <li>• Background content will be disabled</li>
              <li>• Tab switching will trigger warnings</li>
              <li>• Exiting fullscreen will show alerts</li>
              <li>• Stay focused on the demo screen</li>
            </ul>
          </div>

          <div className={`p-4 rounded-lg border-2 ${
            darkMode ? 'bg-red-900/20 border-red-500/30' : 'bg-red-50 border-red-200'
          }`}>
            <h3 className={`font-semibold mb-2 flex items-center ${
              darkMode ? 'text-red-400' : 'text-red-600'
            }`}>
              <AlertCircle size={16} className="mr-2" />
              Important Notes:
            </h3>
            <ul className={`space-y-1 text-sm ${
              darkMode ? 'text-red-300' : 'text-red-700'
            }`}>
              <li>• Do not exit fullscreen during recording</li>
              <li>• Do not switch browser tabs</li>
              <li>• Ensure stable internet connection</li>
              <li>• Have your project ready to demonstrate</li>
            </ul>
          </div>
        </div>

        <div className="flex gap-4">
          <button
            onClick={() => setShowInstructions(false)}
            className={`flex-1 py-3 px-6 rounded-lg border transition-all duration-300 ${
              darkMode
                ? 'border-white/20 text-white/70 hover:bg-white/5'
                : 'border-gray-300 text-gray-600 hover:bg-gray-50'
            }`}
          >
            Cancel
          </button>
          <button
            onClick={startActualDemo}
            disabled={isLoading}
            className={`flex-1 py-3 px-6 rounded-lg transition-all duration-300 ${
              darkMode
                ? 'bg-white text-[#00001a] hover:bg-white/90'
                : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className={`w-5 h-5 border-2 rounded-full animate-spin mr-2 ${
                  darkMode
                    ? 'border-[#00001a]/30 border-t-[#00001a]'
                    : 'border-white/30 border-t-white'
                }`}></div>
                Starting Demo...
              </div>
            ) : (
              <>
                <Maximize size={20} className="inline mr-2" />
                Start Demo
              </>
            )}
          </button>
        </div>
        </div>
      </div>
    </div>
  )

  // Fullscreen Demo Interface
  const FullscreenDemo = () => (
    <div className="fixed inset-0 z-50 bg-black flex flex-col" style={{ pointerEvents: 'all' }}>
      {/* Demo Header */}
      <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
            <span className="text-sm font-medium">
              {isRecording ? 'Recording' : 'Demo Mode'}
            </span>
          </div>
          {isRecording && (
            <div className="text-sm">
              {Math.floor(recordingTime / 60)}:{(recordingTime % 60).toString().padStart(2, '0')} / 2:00
            </div>
          )}
        </div>
        <button
          onClick={exitFullscreen}
          className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      {/* Demo Content Area */}
      <div className="flex-1 flex items-center justify-center bg-gray-100">
        <div className="text-center max-w-2xl mx-auto p-8">
          <div className="mb-8">
            <Video size={64} className="mx-auto mb-4 text-gray-600" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Live Demo Recording</h2>
            <p className="text-gray-600">
              {!isRecording
                ? 'Setting up cameras and preparing demo environment...'
                : 'Recording in progress. Demonstrate your coding skills and explain your approach.'
              }
            </p>
          </div>

          {/* Camera Status */}
          <div className="grid grid-cols-2 gap-4 mb-8">
            <div className={`p-4 rounded-lg border-2 ${
              cameraStatus.face ? 'border-green-500 bg-green-50' : 'border-gray-300 bg-gray-50'
            }`}>
              <Camera size={32} className={`mx-auto mb-2 ${
                cameraStatus.face ? 'text-green-600' : 'text-gray-400'
              }`} />
              <p className={`text-sm font-medium ${
                cameraStatus.face ? 'text-green-700' : 'text-gray-500'
              }`}>
                Face Camera
              </p>
              <p className={`text-xs ${
                cameraStatus.face ? 'text-green-600' : 'text-gray-400'
              }`}>
                {cameraStatus.face ? 'Connected' : 'Connecting...'}
              </p>
            </div>
            <div className={`p-4 rounded-lg border-2 ${
              cameraStatus.phone ? 'border-green-500 bg-green-50' : 'border-gray-300 bg-gray-50'
            }`}>
              <Smartphone size={32} className={`mx-auto mb-2 ${
                cameraStatus.phone ? 'text-green-600' : 'text-gray-400'
              }`} />
              <p className={`text-sm font-medium ${
                cameraStatus.phone ? 'text-green-700' : 'text-gray-500'
              }`}>
                Phone Camera
              </p>
              <p className={`text-xs ${
                cameraStatus.phone ? 'text-green-600' : 'text-gray-400'
              }`}>
                {cameraStatus.phone ? 'Connected' : phoneLink ? `Open: ${phoneLink}` : 'Generating link...'}
              </p>
            </div>
          </div>

          {isRecording && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-700 text-sm">
                <strong>Recording in progress:</strong> Demonstrate your project, explain your code,
                and walk through your problem-solving approach. You have {120 - recordingTime} seconds remaining.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Warning Overlays */}
      {fullscreenExitWarning && (
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg z-10">
          <div className="flex items-center gap-2">
            <AlertCircle size={20} />
            <span>Warning: Please stay in fullscreen mode during the demo!</span>
          </div>
        </div>
      )}

      {tabSwitchWarning && (
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2 bg-orange-600 text-white px-6 py-3 rounded-lg shadow-lg z-10">
          <div className="flex items-center gap-2">
            <EyeOff size={20} />
            <span>Warning: Tab switching detected! Please focus on the demo.</span>
          </div>
        </div>
      )}
    </div>
  )

  // Project Links Modal Component
  const ProjectLinksModal = () => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4">
      <div className={`max-w-2xl w-full max-h-[90vh] rounded-2xl overflow-hidden ${
        darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-gray-200'
      }`}>
        <div className="overflow-y-auto max-h-[90vh] p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Portfolio Submission
            </h2>
            <button
              onClick={() => setShowProjectLinksModal(false)}
              className={`p-2 rounded-lg transition-colors ${
                darkMode ? 'hover:bg-white/10 text-white' : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <X size={24} />
            </button>
          </div>

          <div className="space-y-6">
            {/* File Upload Section */}
            <div>
              <div className="flex items-center mb-3">
                <FileText className={`w-5 h-5 mr-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} />
                <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Upload Portfolio Files
                </h3>
              </div>
              <div className={`border-2 border-dashed rounded-lg p-6 text-center ${
                darkMode ? 'border-white/30 bg-white/5' : 'border-gray-300 bg-gray-50'
              }`}>
                <Upload className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-white/60' : 'text-gray-400'}`} />
                <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Click to browse or drag and drop your portfolio files
                </p>
                <p className={`text-xs mt-2 ${darkMode ? 'text-white/50' : 'text-gray-500'}`}>
                  Supported formats: PDF, DOC, DOCX, TXT, MD
                </p>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.md"
                  className="hidden"
                  id="portfolio-files"
                  onChange={async (e) => {
                    const files = Array.from(e.target.files)
                    if (files.length > 0) {
                      // Simulate file upload
                      await new Promise(resolve => setTimeout(resolve, 2000))
                      console.log('Files uploaded:', files.map(f => f.name))
                    }
                  }}
                />
                <label
                  htmlFor="portfolio-files"
                  className={`inline-block mt-4 px-6 py-2 rounded-lg cursor-pointer transition-colors ${
                    darkMode
                      ? 'bg-white text-[#00001a] hover:bg-white/90'
                      : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                  }`}
                >
                  Choose Files
                </label>
              </div>
            </div>

            {/* Project Links Section */}
            <div>
              <div className="flex items-center mb-3">
                <Link className={`w-5 h-5 mr-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} />
                <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Attach Project Link(s)
                </h3>
              </div>
              <p className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                Add links to your projects (GitHub, Behance, Live Demos, etc.)
              </p>

              <div className="space-y-3">
                {projectLinks.map((link, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="flex-1">
                      <input
                        type="url"
                        value={link}
                        onChange={(e) => updateProjectLink(index, e.target.value)}
                        placeholder="https://github.com/username/project or https://example.com"
                        className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                          darkMode
                            ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40'
                            : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-[#00001a]'
                        } focus:outline-none focus:ring-2 focus:ring-opacity-50 ${
                          darkMode ? 'focus:ring-white/20' : 'focus:ring-[#00001a]/20'
                        }`}
                      />
                    </div>
                    {projectLinks.length > 1 && (
                      <button
                        onClick={() => removeProjectLink(index)}
                        className={`p-2 rounded-lg transition-colors ${
                          darkMode ? 'hover:bg-red-900/30 text-red-400' : 'hover:bg-red-50 text-red-600'
                        }`}
                      >
                        <Trash2 size={20} />
                      </button>
                    )}
                  </div>
                ))}
              </div>

              <button
                onClick={addProjectLink}
                className={`mt-3 flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'bg-white/10 text-white hover:bg-white/20'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Plus size={16} />
                Add Another Link
              </button>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <button
                onClick={() => setShowProjectLinksModal(false)}
                className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
                  darkMode
                    ? 'bg-white/10 text-white hover:bg-white/20'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleProjectLinksSubmit()
                  setCompletedSteps(prev => [...prev, 1])
                  setCurrentStep(2)
                }}
                className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
                  darkMode
                    ? 'bg-white text-[#00001a] hover:bg-white/90'
                    : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                }`}
              >
                Submit Portfolio
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Instructions Modal */}
      {showInstructions && <InstructionsModal />}

      {/* Project Links Modal */}
      {showProjectLinksModal && <ProjectLinksModal />}

      {/* Fullscreen Demo */}
      {isFullscreenDemo && <FullscreenDemo />}

      {/* Main Interface */}
      <div className={`h-full w-full transition-all duration-500 ${
        darkMode ? 'bg-[#00001a]' : 'bg-gray-50'
      } ${(showInstructions || isFullscreenDemo) ? 'pointer-events-none blur-md opacity-50' : ''}`}>
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse ${
          darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse ${
          darkMode ? 'bg-white/5' : 'bg-[#00001a]/5'
        }`}></div>
      </div>

      <div className="relative p-6 space-y-6 h-full overflow-y-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className={`text-3xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
            Skill Verification
          </h1>
          <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
            Complete the steps below to verify your skills
          </p>
        </div>

        {/* Progress Tracker */}
        <div className="mb-8">
          <div className="max-w-4xl mx-auto relative">
            {/* Steps Container */}
            <div className="flex justify-between items-start relative">
              {steps.map((step, index) => {
                const completed = isStepCompleted(step.id)
                const active = isStepActive(step.id)

                return (
                  <div key={step.id} className="flex flex-col items-center relative z-10">
                    {/* Step Circle */}
                    <div className={`w-14 h-14 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-500 shadow-lg ${
                      completed
                        ? darkMode
                          ? 'bg-blue-500 text-white border-2 border-blue-500'
                          : 'bg-[#00001a] text-white border-2 border-[#00001a]'
                        : active
                          ? darkMode
                            ? 'bg-blue-500 text-white border-2 border-blue-500'
                            : 'bg-[#00001a] text-white border-2 border-[#00001a]'
                          : darkMode
                            ? 'bg-gray-600 text-gray-300 border-2 border-gray-600'
                            : 'bg-gray-200 text-gray-500 border-2 border-gray-200'
                    }`}>
                      {completed ? <Check size={24} /> : step.id}
                    </div>

                    {/* Step Labels */}
                    <div className="mt-4 text-center max-w-[140px]">
                      <div className={`text-base font-bold mb-1 ${
                        active || completed
                          ? darkMode ? 'text-white' : 'text-[#00001a]'
                          : darkMode ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {step.title}
                      </div>
                      <div className={`text-sm ${
                        active || completed
                          ? darkMode ? 'text-gray-300' : 'text-[#00001a]/70'
                          : darkMode ? 'text-gray-500' : 'text-gray-500'
                      }`}>
                        {step.subtitle}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Connection Lines - Start from circle 1 edge and end at circle 3 edge */}
            <div className="absolute top-7 z-0 flex justify-between" style={{ left: '28px', right: '28px' }}>
              {/* Single continuous line from step 1 to step 3 */}
              <div className="flex w-full items-center">
                {/* Line 1-2 */}
                <div
                  className={`flex-1 h-1 transition-all duration-500 ${
                    isStepCompleted(1) || isStepCompleted(2) || isStepActive(1)
                      ? darkMode ? 'bg-blue-500' : 'bg-[#00001a]'
                      : darkMode ? 'bg-gray-600' : 'bg-gray-300'
                  }`}
                />

                {/* Gap for middle circle */}
                <div className="w-14"></div>

                {/* Line 2-3 */}
                <div
                  className={`flex-1 h-1 transition-all duration-500 ${
                    isStepCompleted(2) || isStepCompleted(3) || isStepActive(2)
                      ? darkMode ? 'bg-blue-500' : 'bg-[#00001a]'
                      : darkMode ? 'bg-gray-600' : 'bg-gray-300'
                  }`}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Instruction Text */}
        <div className="text-center mb-6">
          <p className={`${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
            Choose your verification level to get started
          </p>
        </div>

        {/* Verification Tier Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {steps.map((step) => {
            const StepIcon = step.icon
            const completed = isStepCompleted(step.id)
            const active = isStepActive(step.id)
            const locked = isStepLocked(step.id)

            return (
              <div
                key={step.id}
                className={`relative rounded-lg border transition-all duration-500 backdrop-blur-xl ${
                  completed
                    ? darkMode
                      ? 'bg-white/5 border-white/30'
                      : 'bg-white border-[#00001a]/30'
                    : active
                      ? darkMode
                        ? 'bg-white/3 border-white/20 hover:bg-white/5'
                        : 'bg-white border-[#00001a]/30 hover:bg-white'
                      : darkMode
                        ? 'bg-white/3 border-white/10'
                        : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30'
                } ${locked ? 'opacity-60' : ''}`}
                style={darkMode ? {
                  boxShadow: active
                    ? '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)'
                    : completed
                      ? '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.3)'
                      : '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: active
                    ? '0 4px 20px rgba(0, 0, 26, 0.12), 0 2px 8px rgba(0, 0, 26, 0.08)'
                    : completed
                      ? '0 4px 15px rgba(0, 0, 26, 0.1), 0 2px 6px rgba(0, 0, 26, 0.06)'
                      : '0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (!locked) {
                    if (darkMode) {
                      if (active) {
                        e.currentTarget.style.boxShadow = '0 0 18px rgba(59, 130, 246, 0.4), 0 0 35px rgba(59, 130, 246, 0.2), 0 6px 20px rgba(0, 0, 0, 0.2)';
                      } else if (completed) {
                        e.currentTarget.style.boxShadow = '0 0 18px rgba(59, 130, 246, 0.4), 0 0 35px rgba(59, 130, 246, 0.2), 0 6px 20px rgba(0, 0, 0, 0.2)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 8px rgba(0, 0, 0, 0.2)';
                      }
                    } else {
                      if (active) {
                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1), 0 0 0 1px rgba(0, 0, 26, 0.05)';
                      } else if (completed) {
                        e.currentTarget.style.boxShadow = '0 6px 25px rgba(0, 0, 26, 0.12), 0 3px 10px rgba(0, 0, 26, 0.08), 0 0 0 1px rgba(0, 0, 26, 0.04)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 26, 0.08), 0 3px 8px rgba(0, 0, 26, 0.06), 0 0 0 1px rgba(0, 0, 26, 0.03)';
                      }
                    }
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    if (active) {
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                    } else if (completed) {
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.3)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                    }
                  } else {
                    if (active) {
                      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 26, 0.12), 0 2px 8px rgba(0, 0, 26, 0.08)';
                    } else if (completed) {
                      e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 26, 0.1), 0 2px 6px rgba(0, 0, 26, 0.06)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04)';
                    }
                  }
                }}
              >
                {/* Lock Overlay */}
                {locked && (
                  <div className={`absolute inset-0 rounded-lg flex items-center justify-center z-10 ${
                    darkMode ? 'bg-[#00001a]/80' : 'bg-gray-100/80'
                  }`}>
                    <div className="text-center">
                      <Lock className={`w-8 h-8 mx-auto mb-2 ${
                        darkMode ? 'text-white/40' : 'text-gray-400'
                      }`} />
                      <p className={`text-sm font-medium ${
                        darkMode ? 'text-white/60' : 'text-gray-500'
                      }`}>
                        {step.id === 2
                          ? 'Complete Portfolio Submission first'
                          : step.id === 3
                            ? 'Demo must be approved first'
                            : `Complete Step ${step.id - 1} first`
                        }
                      </p>
                    </div>
                  </div>
                )}

                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      completed
                        ? darkMode
                          ? 'bg-blue-900/50 text-blue-400'
                          : 'bg-white text-[#00001a] border-2 border-[#00001a]'
                        : active
                          ? darkMode
                            ? 'bg-white/10 text-white'
                            : 'bg-[#00001a]/10 text-[#00001a]'
                          : darkMode
                            ? 'bg-white/5 text-white/40'
                            : 'bg-gray-100 text-gray-400'
                    }`}>
                      {completed ? <Check size={24} /> : <StepIcon size={24} />}
                    </div>

                    {completed && (
                      <div className={`flex items-center text-sm font-medium ${
                        darkMode ? 'text-blue-400' : 'text-[#00001a]'
                      }`}>
                        <Check size={16} className="mr-1" />
                        Complete
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <h3 className={`text-lg font-bold mb-2 ${
                    completed
                      ? darkMode ? 'text-green-400' : 'text-[#00001a]'
                      : active
                        ? darkMode ? 'text-white' : 'text-[#00001a]'
                        : darkMode ? 'text-white/60' : 'text-gray-600'
                  }`}>
                    {step.title}
                  </h3>

                  <p className={`text-sm mb-4 ${
                    completed
                      ? darkMode ? 'text-green-400/80' : 'text-[#00001a]/70'
                      : active
                        ? darkMode ? 'text-white/70' : 'text-[#00001a]/70'
                        : darkMode ? 'text-white/50' : 'text-gray-500'
                  }`}>
                    {step.description}
                  </p>

                  {/* Features List */}
                  <div className="space-y-2 mb-6">
                    {step.id === 1 && (
                      <>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <FileText size={16} className="mr-2" />
                          Project documentation
                        </div>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <CheckCircle size={16} className="mr-2" />
                          Problem-solution explanations
                        </div>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <ExternalLink size={16} className="mr-2" />
                          Project links (GitHub, Live Demos, etc.)
                        </div>
                      </>
                    )}

                    {step.id === 2 && (
                      <>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <Camera size={16} className="mr-2" />
                          Laptop camera for face
                        </div>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <Smartphone size={16} className="mr-2" />
                          Phone camera for keyboard/screen
                        </div>
                        {step2Status === 'submitted' && (
                          <div className={`flex items-center text-sm ${
                            darkMode ? 'text-yellow-400' : 'text-yellow-600'
                          }`}>
                            <Clock size={16} className="mr-2" />
                            Submitted for Review
                          </div>
                        )}
                        {isRecording && (
                          <div className={`flex items-center text-sm ${
                            darkMode ? 'text-red-400' : 'text-red-600'
                          }`}>
                            <Timer size={16} className="mr-2" />
                            Recording: {Math.floor(recordingTime / 60)}:{(recordingTime % 60).toString().padStart(2, '0')} / 2:00
                          </div>
                        )}
                      </>
                    )}

                    {step.id === 3 && (
                      <>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <MessageSquare size={16} className="mr-2" />
                          Solve exactly 10 seeker queries
                        </div>
                        <div className={`flex items-center text-sm ${
                          darkMode ? 'text-white/60' : 'text-gray-600'
                        }`}>
                          <CheckCircle size={16} className="mr-2" />
                          Each query must be properly explained
                        </div>
                        {step2Status === 'approved' && (
                          <div className={`flex items-center text-sm ${
                            darkMode ? 'text-blue-400' : 'text-blue-600'
                          }`}>
                            <Timer size={16} className="mr-2" />
                            Solved Queries: {queriesSolved} / 10
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  {/* Action Button */}
                  {!locked && (
                    <button
                      onClick={() => handleStepAction(step.id)}
                      disabled={completed || isLoading || (step.id === 2 && step2Status === 'submitted') || (step.id === 3 && step2Status !== 'approved')}
                      className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                        completed
                          ? darkMode
                            ? 'bg-green-900/50 text-green-400 cursor-default'
                            : 'bg-white text-[#00001a] border-2 border-[#00001a] cursor-default'
                          : step.id === 2 && step2Status === 'submitted'
                            ? darkMode
                              ? 'bg-yellow-900/50 text-yellow-400 cursor-default'
                              : 'bg-yellow-100 text-yellow-700 cursor-default'
                          : active
                            ? darkMode
                              ? 'bg-white text-[#00001a] hover:bg-white/90 shadow-md hover:shadow-lg'
                              : 'bg-[#00001a] text-white hover:bg-[#00001a]/90 shadow-md hover:shadow-lg'
                            : darkMode
                              ? 'bg-white/10 text-white/50 cursor-not-allowed'
                              : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center">
                          <div className={`w-5 h-5 border-2 rounded-full animate-spin mr-2 ${
                            darkMode
                              ? 'border-[#00001a]/30 border-t-[#00001a]'
                              : 'border-white/30 border-t-white'
                          }`}></div>
                          {step.id === 2 ? 'Starting Demo...' : 'Processing...'}
                        </div>
                      ) : completed ? (
                        <div className="flex items-center gap-1">
                          <Check size={14} className={darkMode ? 'text-green-400' : 'text-[#00001a]'} />
                          Completed
                        </div>
                      ) : step.id === 1 ? (
                        'Submit Portfolio'
                      ) : step.id === 2 ? (
                        step2Status === 'submitted' ? 'Submitted for Review' : 'Start Live Demo'
                      ) : step.id === 3 ? (
                        step2Status !== 'approved' ? '🔒 Demo Must Be Approved' : 'Start Query Validation'
                      ) : (
                        'Start'
                      )}
                    </button>
                  )}

                  {/* Camera Status for Step 2 */}
                  {step.id === 2 && (cameraStatus.face || cameraStatus.phone) && (
                    <div className="mt-4 space-y-2">
                      <div className={`flex items-center text-sm ${
                        cameraStatus.face
                          ? darkMode ? 'text-green-400' : 'text-[#00001a]'
                          : darkMode ? 'text-red-400' : 'text-red-600'
                      }`}>
                        <Camera size={16} className="mr-2" />
                        Face camera: {cameraStatus.face ? (
                          <span className="flex items-center gap-1">
                            <Check size={14} className={darkMode ? 'text-green-400' : 'text-[#00001a]'} />
                            Connected
                          </span>
                        ) : (
                          <span className="flex items-center gap-1">
                            <X size={14} className={darkMode ? 'text-red-400' : 'text-red-600'} />
                            Disconnected
                          </span>
                        )}
                      </div>
                      <div className={`flex items-center text-sm ${
                        cameraStatus.phone
                          ? darkMode ? 'text-green-400' : 'text-[#00001a]'
                          : darkMode ? 'text-red-400' : 'text-red-600'
                      }`}>
                        <Smartphone size={16} className="mr-2" />
                        Phone camera: {cameraStatus.phone ? (
                          <span className="flex items-center gap-1">
                            <Check size={14} className={darkMode ? 'text-green-400' : 'text-[#00001a]'} />
                            Connected
                          </span>
                        ) : (
                          <span className="flex items-center gap-1">
                            <X size={14} className={darkMode ? 'text-red-400' : 'text-red-600'} />
                            Disconnected
                          </span>
                        )}
                      </div>
                      {phoneLink && (
                        <div className={`text-xs p-2 rounded border ${
                          darkMode ? 'bg-white/5 border-white/20 text-white/70' : 'bg-gray-50 border-gray-200 text-gray-600'
                        }`}>
                          Phone link: {phoneLink}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* Additional Info */}
        <div className="mt-8 text-center">
          <div className={`rounded-lg border p-6 transition-all duration-500 backdrop-blur-xl ${
            darkMode
              ? 'bg-white/3 border-white/20'
              : 'bg-white border-gray-200'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
          } : {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transition: 'all 0.3s ease'
          }}>
            <h3 className={`text-lg font-semibold mb-3 ${
              darkMode ? 'text-white' : 'text-[#00001a]'
            }`}>
              Verification Benefits
            </h3>
            <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 text-sm ${
              darkMode ? 'text-white/70' : 'text-[#00001a]/70'
            }`}>
              <div className="flex items-center justify-center">
                <Check size={16} className={`mr-2 ${
                  darkMode ? 'text-green-400' : 'text-[#00001a]'
                }`} />
                Enhanced profile credibility
              </div>
              <div className="flex items-center justify-center">
                <Check size={16} className={`mr-2 ${
                  darkMode ? 'text-green-400' : 'text-[#00001a]'
                }`} />
                Priority in query matching
              </div>
              <div className="flex items-center justify-center">
                <Check size={16} className={`mr-2 ${
                  darkMode ? 'text-green-400' : 'text-[#00001a]'
                }`} />
                Access to premium features
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

export default SkillVerification
