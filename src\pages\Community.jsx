import React, { useState, useRef, useEffect } from 'react'
import {
  Users,
  MessageCircle,
  Trophy,
  Calendar,
  Share2,
  Plus,
  Bell,
  Check,
  Clock,
  MapPin,
  User,
  Shield,
  Star,
  Eye,
  MessageSquare,
  ThumbsUp,
  UserCheck,
  Award,
  Target,
  Zap,
  Crown,
  Lock,
  Search,
  Edit,
  Trash2,
  UserPlus,
  X,
  Link,
  Copy,
  Send,
  Paperclip,
  AlertCircle,
  Download
} from 'lucide-react'

const Community = ({ darkMode }) => {
  // State management
  const [userpsudoname, setUserpsudoname] = useState('')
  const [notifications, setNotifications] = useState([])
  const [showNotification, setShowNotification] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Tab functionality
  const [activeTab, setActiveTab] = useState('my-communities')

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState('all') // 'all', 'community', 'topic'
  const [eventsSearchQuery, setEventsSearchQuery] = useState('')
  const [workshopsSearchQuery, setWorkshopsSearchQuery] = useState('')
  const [conferencesSearchQuery, setConferencesSearchQuery] = useState('')

  // Community request modal
  const [showRequestModal, setShowRequestModal] = useState(false)
  const [communityRequest, setCommunityRequest] = useState({
    name: '',
    purpose: '',
    scope: '',
    technology: ''
  })

  // User's community management
  const [userCommunity, setUserCommunity] = useState(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showMembersModal, setShowMembersModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)

  // All communities and join status
  const [joinedCommunities, setJoinedCommunities] = useState(new Set())

  // Chat functionality
  const [activeChatCommunity, setActiveChatCommunity] = useState(null)
  const [chatMessages, setChatMessages] = useState({
    // Sample tech-related chat messages with metadata
    1: [ // Python Developers
      {
        id: 1,
        sender: 'alex_python',
        message: 'Check out this machine learning model I built!',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        type: 'text'
      },
      {
        id: 2,
        sender: 'sarah_dev',
        message: '',
        timestamp: new Date(Date.now() - 3000000).toISOString(),
        type: 'file',
        fileData: {
          name: 'neural_network_architecture.png',
          size: 245760,
          type: 'image/png',
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5ldXJhbCBOZXR3b3JrPC90ZXh0Pjwvc3ZnPg==',
          isImage: true,
          isVideo: false,
          isGif: false,
          isDocument: false,
          isArchive: false,
          duration: 0,
          showPreview: true
        }
      },
      {
        id: 3,
        sender: 'mike_ml',
        message: 'Here\'s the training dataset for the project',
        timestamp: new Date(Date.now() - 2400000).toISOString(),
        type: 'file',
        fileData: {
          name: 'training_data.csv',
          size: 1048576,
          type: 'text/csv',
          url: '#',
          isImage: false,
          isVideo: false,
          isGif: false,
          isDocument: true,
          isArchive: false,
          duration: 0,
          showPreview: false
        }
      },
      {
        id: 4,
        sender: 'lisa_ai',
        message: '',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        type: 'file',
        fileData: {
          name: 'python_tutorial.mp4',
          size: 8388608, // 8MB
          type: 'video/mp4',
          url: 'data:video/mp4;base64,',
          isImage: false,
          isVideo: true,
          isGif: false,
          isDocument: false,
          isArchive: false,
          duration: 25, // 25 seconds
          showPreview: true
        }
      },
      {
        id: 5,
        sender: 'john_coder',
        message: 'Latest Python code for the API',
        timestamp: new Date(Date.now() - 1200000).toISOString(),
        type: 'file',
        fileData: {
          name: 'api_endpoints.py',
          size: 15360,
          type: 'text/x-python',
          url: '#',
          isImage: false,
          isVideo: false,
          isGif: false,
          isDocument: true,
          isArchive: false,
          duration: 0,
          showPreview: false
        }
      }
    ],
    2: [ // React Developers
      {
        id: 6,
        sender: 'emma_react',
        message: 'New component design mockup',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        type: 'file',
        fileData: {
          name: 'component_mockup.png',
          size: 512000,
          type: 'image/png',
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNjFkYWZiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzIxMzU0NyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPlJlYWN0IENvbXBvbmVudDwvdGV4dD48L3N2Zz4=',
          isImage: true,
          isVideo: false,
          isGif: false,
          isDocument: false,
          isArchive: false,
          duration: 0,
          showPreview: true
        }
      },
      {
        id: 7,
        sender: 'david_frontend',
        message: '',
        timestamp: new Date(Date.now() - 2700000).toISOString(),
        type: 'file',
        fileData: {
          name: 'react_animation.gif',
          size: 2097152,
          type: 'image/gif',
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmY2MzQ3Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkFuaW1hdGVkIEdJRjwvdGV4dD48L3N2Zz4=',
          isImage: false,
          isVideo: false,
          isGif: true,
          isDocument: false,
          isArchive: false,
          duration: 0,
          showPreview: true
        }
      },
      {
        id: 8,
        sender: 'anna_ui',
        message: 'Component library documentation',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        type: 'file',
        fileData: {
          name: 'component_docs.pdf',
          size: 3145728,
          type: 'application/pdf',
          url: '#',
          isImage: false,
          isVideo: false,
          isGif: false,
          isDocument: true,
          isArchive: false,
          duration: 0,
          showPreview: false
        }
      }
    ],
    3: [ // Data Science Hub
      {
        id: 9,
        sender: 'robert_data',
        message: 'Data visualization results',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        type: 'file',
        fileData: {
          name: 'data_visualization.png',
          size: 768000,
          type: 'image/png',
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjOGI1Y2Y2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkRhdGEgVml6PC90ZXh0Pjwvc3ZnPg==',
          isImage: true,
          isVideo: false,
          isGif: false,
          isDocument: false,
          isArchive: false,
          duration: 0,
          showPreview: true
        }
      },
      {
        id: 10,
        sender: 'maria_stats',
        message: 'Machine learning model demo',
        timestamp: new Date(Date.now() - 2400000).toISOString(),
        type: 'file',
        fileData: {
          name: 'ml_model_demo.mp4',
          size: 15728640, // 15MB - large video
          type: 'video/mp4',
          url: '#',
          isImage: false,
          isVideo: true,
          isGif: false,
          isDocument: false,
          isArchive: false,
          duration: 120, // 2 minutes
          showPreview: false
        }
      }
    ]
  })
  const [newMessage, setNewMessage] = useState('')
  const [messageReactions, setMessageReactions] = useState({})
  const [showReactionPicker, setShowReactionPicker] = useState(null)
  const [hoveredMessage, setHoveredMessage] = useState(null)

  // View Details Modal
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [detailsItem, setDetailsItem] = useState(null)
  const [detailsType, setDetailsType] = useState(null) // 'community', 'event', 'workshop', 'conference'

  // File attachment
  const [attachedFiles, setAttachedFiles] = useState({})

  // Reaction emojis
  const reactionEmojis = ['❤️', '👍', '👎', '😂', '😮', '😡', '🔥', '😱']

  // Close reaction picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showReactionPicker && !event.target.closest('.reaction-picker')) {
        setShowReactionPicker(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showReactionPicker])

  // Cleanup file URLs when component unmounts
  useEffect(() => {
    return () => {
      Object.values(attachedFiles).forEach(fileData => {
        if (fileData.url) {
          URL.revokeObjectURL(fileData.url)
        }
      })
    }
  }, [])

  // Handle message reactions
  const handleReaction = (messageId, emoji) => {
    setMessageReactions(prev => {
      const messageReacts = prev[messageId] || {}
      const emojiReacts = messageReacts[emoji] || { count: 0, users: [] }

      // Check if current user already reacted with this emoji
      const currentUser = 'currentUser' // This would be the actual current user ID
      const hasReacted = emojiReacts.users.includes(currentUser)

      if (hasReacted) {
        // Remove reaction
        const newUsers = emojiReacts.users.filter(user => user !== currentUser)
        const newCount = Math.max(0, emojiReacts.count - 1)

        if (newCount === 0) {
          // Remove emoji entirely if no reactions left
          const { [emoji]: removed, ...restEmojis } = messageReacts
          return {
            ...prev,
            [messageId]: restEmojis
          }
        } else {
          return {
            ...prev,
            [messageId]: {
              ...messageReacts,
              [emoji]: { count: newCount, users: newUsers }
            }
          }
        }
      } else {
        // Add reaction
        return {
          ...prev,
          [messageId]: {
            ...messageReacts,
            [emoji]: {
              count: emojiReacts.count + 1,
              users: [...emojiReacts.users, currentUser]
            }
          }
        }
      }
    })
    setShowReactionPicker(null)
  }

  // Handle showing details modal
  const handleShowDetails = (item, type) => {
    setDetailsItem(item)
    setDetailsType(type)
    setShowDetailsModal(true)
  }

  // File handling functions
  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    switch (extension) {
      // Documents
      case 'pdf':
        return '📄'
      case 'doc':
      case 'docx':
        return '📝'
      case 'txt':
      case 'rtf':
        return '📄'
      case 'odt':
        return '📝'

      // Spreadsheets
      case 'xls':
      case 'xlsx':
      case 'csv':
        return '📊'

      // Presentations
      case 'ppt':
      case 'pptx':
        return '📋'

      // Archives
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return '🗜️'

      // Images
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'webp':
      case 'bmp':
      case 'svg':
        return '🖼️'
      case 'gif':
        return '🎞️'

      // Videos
      case 'mp4':
      case 'webm':
      case 'avi':
      case 'mov':
      case 'mkv':
        return '🎥'

      // Audio
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'ogg':
      case 'aac':
        return '🎵'

      // Code files
      case 'js':
      case 'jsx':
        return '⚡'
      case 'ts':
      case 'tsx':
        return '🔷'
      case 'py':
        return '🐍'
      case 'java':
        return '☕'
      case 'cpp':
      case 'c':
        return '⚙️'
      case 'html':
      case 'htm':
        return '🌐'
      case 'css':
        return '🎨'
      case 'json':
        return '📋'
      case 'xml':
        return '📄'
      case 'sql':
        return '🗃️'

      // Executables
      case 'exe':
      case 'msi':
        return '⚙️'
      case 'dmg':
        return '💿'
      case 'deb':
      case 'rpm':
        return '📦'

      // Unknown/fallback
      default:
        return '📎'
    }
  }

  const isImageFile = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(extension)
  }

  const isVideoFile = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    return ['mp4', 'webm', 'avi', 'mov', 'mkv'].includes(extension)
  }

  const isGifFile = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    return extension === 'gif'
  }

  const isDocumentFile = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    return ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)
  }

  const isArchiveFile = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase()
    return ['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)
  }

  const shouldShowPreview = (fileData, duration = 0) => {
    const { name, size } = fileData

    // Images (except GIFs) - always show preview
    if (isImageFile(name) && !isGifFile(name)) {
      return true
    }

    // GIFs - show preview
    if (isGifFile(name)) {
      return true
    }

    // Short videos (≤ 10MB and ≤ 30 seconds) - show preview with controls
    if (isVideoFile(name) && size <= 10 * 1024 * 1024 && (duration === 0 || duration <= 30)) {
      return true
    }

    // Everything else - no preview
    return false
  }

  const getVideoDuration = (file) => {
    return new Promise((resolve) => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.onloadedmetadata = () => {
        resolve(video.duration)
        URL.revokeObjectURL(video.src)
      }
      video.onerror = () => resolve(0)
      video.src = URL.createObjectURL(file)
    })
  }

  const formatDuration = (seconds) => {
    if (seconds < 60) {
      return `${Math.floor(seconds)}s`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}:${minutes.toString().padStart(2, '0')}:00`
    }
  }

  const getFileTooltip = (fileData) => {
    if (fileData.showPreview) {
      if (fileData.isImage && !fileData.isGif) {
        return "Preview this image"
      } else if (fileData.isGif) {
        return "GIF preview - Click to download"
      } else if (fileData.isVideo) {
        return "Video preview - Use controls to play"
      }
    } else {
      if (fileData.isDocument) {
        return "Click to download document"
      } else if (fileData.isArchive) {
        return "Click to download archive"
      } else if (fileData.isVideo) {
        return "Large video - Click to download"
      } else {
        return "Click to download"
      }
    }
    return "Click to download"
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownloadFile = (fileData) => {
    try {
      const link = document.createElement('a')
      link.href = fileData.url
      link.download = fileData.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      showNotificationMessage(`Downloading ${fileData.name}...`, 'success')
    } catch (error) {
      showNotificationMessage('Failed to download file.', 'error')
    }
  }

  // Events
  const [joinedEvents, setJoinedEvents] = useState(new Set())

  // View more states
  const [showAllCommunities, setShowAllCommunities] = useState(false)
  const [showAllEvents, setShowAllEvents] = useState(false)

  // Load user psudoname from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('userPseudonym')
    if (savedData) {
      const { pseudonym } = JSON.parse(savedData)
      setUserpsudoname(pseudonym)
    }
  }, [])

  // Notification system
  const showNotificationMessage = (message, type = 'success') => {
    const notification = {
      id: Date.now(),
      message,
      type
    }
    setNotifications(prev => [...prev, notification])
    setShowNotification(true)

    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
      if (notifications.length <= 1) {
        setShowNotification(false)
      }
    }, 3000)
  }

  // Tech topics for filtering
  const techTopics = [
    'Python', 'JavaScript', 'Java', 'C++', 'C', 'React', 'Node.js', 'Angular',
    'Vue.js', 'Django', 'Flask', 'Spring', 'Express', 'MongoDB', 'PostgreSQL',
    'MySQL', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP', 'Git',
    'Machine Learning', 'Data Science', 'AI', 'Deep Learning', 'TensorFlow',
    'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn', 'HTML', 'CSS', 'TypeScript',
    'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin', 'Flutter', 'React Native'
  ]

  // Sample communities data
  const allCommunities = [
    {
      id: 1,
      name: 'Python Developers',
      purpose: 'Share Python knowledge and best practices',
      scope: 'Programming discussions, code reviews, tutorials',
      technology: 'Python',
      members: 1247,
      creator: 'CodeMaster99',
      isApproved: true
    },
    {
      id: 2,
      name: 'React Enthusiasts',
      purpose: 'Modern React development techniques',
      scope: 'Component patterns, hooks, performance optimization',
      technology: 'React',
      members: 892,
      creator: 'ReactGuru',
      isApproved: true
    },
    {
      id: 3,
      name: 'Machine Learning Hub',
      purpose: 'ML algorithms and model deployment',
      scope: 'Research papers, model training, deployment strategies',
      technology: 'Machine Learning',
      members: 1567,
      creator: 'MLExpert',
      isApproved: true
    },
    {
      id: 4,
      name: 'JavaScript Masters',
      purpose: 'Advanced JavaScript concepts and frameworks',
      scope: 'ES6+, async programming, framework comparisons',
      technology: 'JavaScript',
      members: 2103,
      creator: 'JSNinja',
      isApproved: true
    }
  ]

  // Mock members for community management
  const mockMembers = [
    { id: 1, name: 'CodeNinja47', role: 'Member', joinDate: '2024-01-15' },
    { id: 2, name: 'DataWizard', role: 'Moderator', joinDate: '2024-02-20' },
    { id: 3, name: 'PixelMaster', role: 'Member', joinDate: '2024-03-10' },
    { id: 4, name: 'CloudSurfer', role: 'Member', joinDate: '2024-03-25' }
  ]

  // Upcoming events data
  const upcomingEvents = [
    {
      id: 1,
      topic: 'React 19 Features Deep Dive',
      communityName: 'React Enthusiasts',
      date: '2025-07-30',
      time: '14:00',
      attendees: 156,
      isLive: false
    },
    {
      id: 2,
      topic: 'Python Machine Learning Workshop',
      communityName: 'Python Developers',
      date: '2025-08-02',
      time: '16:30',
      attendees: 234,
      isLive: false
    },
    {
      id: 3,
      topic: 'JavaScript Performance Optimization',
      communityName: 'JavaScript Masters',
      date: '2025-08-05',
      time: '13:00',
      attendees: 189,
      isLive: false
    },
    {
      id: 4,
      topic: 'AI Model Deployment Strategies',
      communityName: 'Machine Learning Hub',
      date: '2025-08-08',
      time: '15:00',
      attendees: 267,
      isLive: false
    }
  ]

  // Workshops data
  const workshops = [
    {
      id: 2,
      title: 'React Advanced Patterns Workshop',
      type: 'Workshop',
      date: '2025-08-12',
      time: '14:00',
      hostCommunity: 'React Enthusiasts',
      description: 'Deep dive into advanced React patterns including custom hooks, context optimization, and performance techniques.',
      topics: ['React', 'JavaScript', 'Frontend', 'Performance'],
      registrationOpen: true,
      attendees: 85
    },
    {
      id: 4,
      title: 'Python Data Science Workshop',
      type: 'Workshop',
      date: '2025-08-18',
      time: '13:30',
      hostCommunity: 'Python Developers',
      description: 'Hands-on workshop covering pandas, numpy, matplotlib, and scikit-learn for data analysis.',
      topics: ['Python', 'Data Science', 'Analytics', 'Machine Learning'],
      registrationOpen: true,
      attendees: 120
    },
    {
      id: 6,
      title: 'DevOps & Cloud Infrastructure Workshop',
      type: 'Workshop',
      date: '2025-08-22',
      time: '15:00',
      hostCommunity: 'DevOps Engineers',
      description: 'Learn about containerization, orchestration, and cloud deployment strategies.',
      topics: ['DevOps', 'Cloud', 'Docker', 'Kubernetes'],
      registrationOpen: true,
      attendees: 95
    }
  ]

  // Conferences data
  const conferences = [
    {
      id: 1,
      title: 'AI & Machine Learning Summit 2025',
      type: 'Conference',
      date: '2025-08-15',
      time: '09:00',
      hostCommunity: 'Machine Learning Hub',
      description: 'A comprehensive conference covering the latest trends in AI and ML, featuring industry experts and hands-on workshops.',
      topics: ['AI', 'Machine Learning', 'Deep Learning', 'Neural Networks'],
      registrationOpen: true,
      attendees: 450
    },
    {
      id: 3,
      title: 'Cybersecurity Best Practices Conference',
      type: 'Conference',
      date: '2025-08-20',
      time: '10:00',
      hostCommunity: 'Security Professionals',
      description: 'Learn about the latest cybersecurity threats and defense strategies from industry leaders.',
      topics: ['Cybersecurity', 'Security', 'Networking', 'Ethical Hacking'],
      registrationOpen: true,
      attendees: 320
    },
    {
      id: 5,
      title: 'UI/UX Design Thinking Conference',
      type: 'Conference',
      date: '2025-08-25',
      time: '11:00',
      hostCommunity: 'Design Community',
      description: 'Explore modern design thinking methodologies and user experience best practices.',
      topics: ['Design', 'UX', 'UI', 'User Research'],
      registrationOpen: true,
      attendees: 280
    }
  ]

  // Filter communities based on search query
  const filteredCommunities = allCommunities.filter(community => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()

    if (filterType === 'community') {
      return community.name.toLowerCase().includes(query)
    } else if (filterType === 'topic') {
      return community.technology.toLowerCase().includes(query) ||
             techTopics.some(topic => topic.toLowerCase().includes(query) &&
                           community.technology.toLowerCase().includes(topic.toLowerCase()))
    } else {
      return community.name.toLowerCase().includes(query) ||
             community.technology.toLowerCase().includes(query) ||
             community.purpose.toLowerCase().includes(query)
    }
  })

  // Filter events based on search query (topic-based only)
  const filteredEvents = upcomingEvents.filter(event => {
    if (!eventsSearchQuery) return true

    const query = eventsSearchQuery.toLowerCase()
    return event.topic.toLowerCase().includes(query) ||
           event.communityName.toLowerCase().includes(query)
  })

  // Filter workshops based on search query (topic-based only)
  const filteredWorkshops = workshops.filter(workshop => {
    if (!workshopsSearchQuery) return true

    const query = workshopsSearchQuery.toLowerCase()
    return workshop.title.toLowerCase().includes(query) ||
           workshop.description.toLowerCase().includes(query) ||
           workshop.topics.some(topic => topic.toLowerCase().includes(query)) ||
           workshop.hostCommunity.toLowerCase().includes(query)
  })

  // Filter conferences based on search query (topic-based only)
  const filteredConferences = conferences.filter(conference => {
    if (!conferencesSearchQuery) return true

    const query = conferencesSearchQuery.toLowerCase()
    return conference.title.toLowerCase().includes(query) ||
           conference.description.toLowerCase().includes(query) ||
           conference.topics.some(topic => topic.toLowerCase().includes(query)) ||
           conference.hostCommunity.toLowerCase().includes(query)
  })

  // Workshop registration handler
  const handleWorkshopRegistration = (workshopId, workshopTitle) => {
    // Here you would typically make an API call to register for the workshop
    showNotificationMessage(`Successfully registered for ${workshopTitle}!`, 'success')
  }

  // Event handlers
  const handleRequestCommunity = async () => {
    if (!communityRequest.name.trim()) {
      showNotificationMessage('Please enter a community name', 'error')
      return
    }

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Simulate admin approval after 3 seconds
      setTimeout(() => {
        const newCommunity = {
          ...communityRequest,
          id: Date.now(),
          creator: userpsudoname || 'Anonymous',
          status: 'approved',
          members: 1,
          createdAt: new Date().toISOString()
        }
        setUserCommunity(newCommunity)
        showNotificationMessage('Your community has been approved! 🎉', 'success')
      }, 3000)

      setCommunityRequest({ name: '', purpose: '', scope: '', technology: '' })
      setShowRequestModal(false)
      showNotificationMessage('Your community creation request has been submitted to the admin.', 'success')
    } catch (error) {
      showNotificationMessage('Failed to submit request. Please try again.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditCommunity = async (updatedData) => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setUserCommunity(prev => ({ ...prev, ...updatedData }))
      setShowEditModal(false)
      showNotificationMessage('Community updated successfully! ✅', 'success')
    } catch (error) {
      showNotificationMessage('Failed to update community.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteCommunity = async () => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setUserCommunity(null)
      setShowDeleteModal(false)
      showNotificationMessage('Community deleted successfully.', 'info')
    } catch (error) {
      showNotificationMessage('Failed to delete community.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleJoinCommunity = async (communityId) => {
    const isJoined = joinedCommunities.has(communityId)
    setIsLoading(true)

    try {
      await new Promise(resolve => setTimeout(resolve, 800))

      if (isJoined) {
        setJoinedCommunities(prev => {
          const newSet = new Set(prev)
          newSet.delete(communityId)
          return newSet
        })
        showNotificationMessage('Left community successfully!', 'info')
      } else {
        setJoinedCommunities(prev => new Set([...prev, communityId]))
        showNotificationMessage('Joined community successfully! 🎉', 'success')
      }
    } catch (error) {
      showNotificationMessage('Failed to update membership.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChatCommunity = async (community) => {
    if (!userpsudoname) {
      showNotificationMessage('Please create your pseudo name in profile settings to access chat.', 'error')
      return
    }

    setActiveChatCommunity(community)

    // Initialize chat messages if not exists
    if (!chatMessages[community.id]) {
      setChatMessages(prev => ({
        ...prev,
        [community.id]: [
          {
            id: 1,
            sender: 'CodeNinja47',
            message: 'Welcome to the community chat!',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            type: 'text'
          },
          {
            id: 2,
            sender: 'DataWizard',
            message: 'Feel free to share resources and ask questions here.',
            timestamp: new Date(Date.now() - 1800000).toISOString(),
            type: 'text'
          }
        ]
      }))
    }
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !activeChatCommunity) return

    const message = {
      id: Date.now(),
      sender: userpsudoname,
      message: newMessage.trim(),
      timestamp: new Date().toISOString(),
      type: 'text'
    }

    setChatMessages(prev => ({
      ...prev,
      [activeChatCommunity.id]: [...(prev[activeChatCommunity.id] || []), message]
    }))

    setNewMessage('')
    showNotificationMessage('Message sent! 💬', 'success')
  }

  const handleCopyLink = async (communityName) => {
    const shareUrl = `${window.location.origin}/community/${communityName.toLowerCase().replace(/\s+/g, '-')}`

    try {
      await navigator.clipboard.writeText(shareUrl)
      showNotificationMessage('Community link copied to clipboard! 📋', 'success')
    } catch (error) {
      showNotificationMessage('Failed to copy link.', 'error')
    }
  }

  const handleJoinEvent = async (eventId) => {
    const isJoined = joinedEvents.has(eventId)
    setIsLoading(true)

    try {
      await new Promise(resolve => setTimeout(resolve, 800))

      if (isJoined) {
        setJoinedEvents(prev => {
          const newSet = new Set(prev)
          newSet.delete(eventId)
          return newSet
        })
        showNotificationMessage('Left event successfully!', 'info')
      } else {
        setJoinedEvents(prev => new Set([...prev, eventId]))
        showNotificationMessage('Joined event successfully! 🎉', 'success')
      }
    } catch (error) {
      showNotificationMessage('Failed to update event registration.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePromoteMember = async (memberId) => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 600))
      showNotificationMessage('Member promoted successfully! 🎉', 'success')
    } catch (error) {
      showNotificationMessage('Failed to promote member.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveMember = async (memberId) => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 600))
      showNotificationMessage('Member removed successfully.', 'info')
    } catch (error) {
      showNotificationMessage('Failed to remove member.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAttachFile = async () => {
    try {
      // Create a file input element
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.mp3,.wav,.zip,.rar,.js,.jsx,.ts,.tsx,.py,.java,.cpp,.c,.xls,.xlsx,.ppt,.pptx'
      input.onchange = async (e) => {
        const file = e.target.files[0]
        if (file) {
          // Check file size (limit to 10MB)
          if (file.size > 10 * 1024 * 1024) {
            showNotificationMessage('File size must be less than 10MB.', 'error')
            return
          }

          // Create file URL for preview/download
          const fileUrl = URL.createObjectURL(file)
          const fileId = Date.now()

          // Get video duration if it's a video file
          let videoDuration = 0
          if (isVideoFile(file.name)) {
            try {
              videoDuration = await getVideoDuration(file)
            } catch (error) {
              console.log('Could not get video duration:', error)
            }
          }

          // Store file data
          setAttachedFiles(prev => ({
            ...prev,
            [fileId]: {
              file,
              url: fileUrl,
              name: file.name,
              size: file.size,
              type: file.type,
              duration: videoDuration
            }
          }))

          // Create enhanced file data
          const fileData = {
            name: file.name,
            size: file.size,
            type: file.type,
            url: fileUrl,
            isImage: isImageFile(file.name),
            isVideo: isVideoFile(file.name),
            isGif: isGifFile(file.name),
            isDocument: isDocumentFile(file.name),
            isArchive: isArchiveFile(file.name),
            duration: videoDuration,
            showPreview: shouldShowPreview({ name: file.name, size: file.size }, videoDuration)
          }

          // Create file message
          const fileMessage = {
            id: fileId,
            sender: userpsudoname,
            message: '',
            timestamp: new Date().toISOString(),
            type: 'file',
            fileData
          }

          // Add file message to chat
          setChatMessages(prev => ({
            ...prev,
            [activeChatCommunity.id]: [...(prev[activeChatCommunity.id] || []), fileMessage]
          }))

          showNotificationMessage(`File "${file.name}" attached! 📎`, 'success')
        }
      }
      input.click()
    } catch (error) {
      showNotificationMessage('Failed to attach file.', 'error')
    }
  }

  const handleRequestInputChange = (field, value) => {
    setCommunityRequest(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Utility functions
  const renderIcon = (iconName, size = 16) => {
    const iconProps = { size, className: darkMode ? 'text-white' : 'text-[#00001a]' }

    switch (iconName) {
      case 'User': return <User {...iconProps} />
      case 'Target': return <Target {...iconProps} />
      case 'Zap': return <Zap {...iconProps} />
      case 'Shield': return <Shield {...iconProps} />
      case 'Eye': return <Eye {...iconProps} />
      case 'Star': return <Star {...iconProps} />
      case 'Crown': return <Crown {...iconProps} />
      case 'Lock': return <Lock {...iconProps} />
      default: return <User {...iconProps} />
    }
  }





  return (
    <div className={`h-full w-full transition-all duration-500 relative ${
      darkMode ? 'bg-[#00001a]' : 'bg-white'
    }`}>
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className={`p-6 rounded-2xl backdrop-blur-xl border ${
            darkMode
              ? 'bg-white/10 border-white/20'
              : 'bg-white/90 border-[#00001a]/20'
          }`}>
            <div className="flex items-center gap-4">
              <div className={`animate-spin rounded-full h-8 w-8 border-b-2 ${darkMode ? 'border-white' : 'border-[#00001a]'}`}></div>
              <span className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Processing...
              </span>
            </div>
          </div>
        </div>
      )}
      {/* Notification System */}
      {showNotification && notifications.length > 0 && (
        <div className="fixed top-4 right-4 z-50 space-y-2">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`px-4 py-3 rounded-lg shadow-lg backdrop-blur-sm border transition-all duration-300 ${
                notification.type === 'success'
                  ? darkMode
                    ? 'bg-white/20 text-white border-white/30'
                    : 'bg-white text-[#00001a] border-[#00001a]/30'
                  : notification.type === 'error'
                  ? darkMode
                    ? 'bg-white/20 text-white border-white/30'
                    : 'bg-white text-[#00001a] border-[#00001a]/30'
                  : darkMode
                    ? 'bg-white/20 text-white border-white/30'
                    : 'bg-white text-[#00001a] border-[#00001a]/30'
              }`}
            >
              {notification.message}
            </div>
          ))}
        </div>
      )}

      <div className="w-full h-full p-6 flex flex-col">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <p className={`mt-2 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                Connect with fellow developers and share knowledge
              </p>
              {userpsudoname && (
                <p className={`mt-1 text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Your pseudo name: <span className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>{userpsudoname}</span>
                </p>
              )}
            </div>

            {/* Request New Community Button */}
            <button
              onClick={() => setShowRequestModal(true)}
              disabled={isLoading || !userpsudoname}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                !userpsudoname
                  ? darkMode
                    ? 'bg-white/5 border-white/10 text-white/40 cursor-not-allowed'
                    : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                  : darkMode
                    ? 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] cursor-pointer'
                    : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90 hover:shadow-[0_0_15px_rgba(0,0,26,0.3)] cursor-pointer'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={!userpsudoname ? 'Set your pseudoname in Profile to request communities' : 'Request a new community'}
            >
              <Plus size={16} />
              <span className="text-sm font-medium">Request New Community</span>
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="mb-6">
            <div className="flex gap-3 flex-wrap">
              <button
                onClick={() => setActiveTab('my-communities')}
                className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeTab === 'my-communities'
                    ? darkMode
                      ? 'bg-white text-[#00001a] shadow-xl'
                      : 'bg-[#00001a] text-white shadow-xl'
                    : darkMode
                      ? 'bg-[#00001a] text-white hover:bg-[#00001a]/80'
                      : 'bg-white text-[#00001a] hover:bg-[#00001a]/5'
                }`}
                style={{
                  boxShadow: activeTab === 'my-communities'
                    ? darkMode
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : darkMode
                      ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                      : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'my-communities'
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'my-communities'
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                My Communities
              </button>
              <button
                onClick={() => setActiveTab('joined-communities')}
                className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeTab === 'joined-communities'
                    ? darkMode
                      ? 'bg-white text-[#00001a] shadow-xl'
                      : 'bg-[#00001a] text-white shadow-xl'
                    : darkMode
                      ? 'bg-[#00001a] text-white hover:bg-[#00001a]/80'
                      : 'bg-white text-[#00001a] hover:bg-[#00001a]/5'
                }`}
                style={{
                  boxShadow: activeTab === 'joined-communities'
                    ? darkMode
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : darkMode
                      ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                      : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'joined-communities'
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'joined-communities'
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                Joined Communities
              </button>
              <button
                onClick={() => setActiveTab('all-communities')}
                className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeTab === 'all-communities'
                    ? darkMode
                      ? 'bg-white text-[#00001a] shadow-xl'
                      : 'bg-[#00001a] text-white shadow-xl'
                    : darkMode
                      ? 'bg-[#00001a] text-white hover:bg-[#00001a]/80'
                      : 'bg-white text-[#00001a] hover:bg-[#00001a]/5'
                }`}
                style={{
                  boxShadow: activeTab === 'all-communities'
                    ? darkMode
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : darkMode
                      ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                      : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'all-communities'
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'all-communities'
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                All Communities
              </button>
              <button
                onClick={() => setActiveTab('upcoming-events')}
                className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeTab === 'upcoming-events'
                    ? darkMode
                      ? 'bg-white text-[#00001a] shadow-xl'
                      : 'bg-[#00001a] text-white shadow-xl'
                    : darkMode
                      ? 'bg-[#00001a] text-white hover:bg-[#00001a]/80'
                      : 'bg-white text-[#00001a] hover:bg-[#00001a]/5'
                }`}
                style={{
                  boxShadow: activeTab === 'upcoming-events'
                    ? darkMode
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : darkMode
                      ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                      : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'upcoming-events'
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'upcoming-events'
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                Upcoming Events
              </button>
              <button
                onClick={() => setActiveTab('workshops')}
                className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeTab === 'workshops'
                    ? darkMode
                      ? 'bg-white text-[#00001a] shadow-xl'
                      : 'bg-[#00001a] text-white shadow-xl'
                    : darkMode
                      ? 'bg-[#00001a] text-white hover:bg-[#00001a]/80'
                      : 'bg-white text-[#00001a] hover:bg-[#00001a]/5'
                }`}
                style={{
                  boxShadow: activeTab === 'workshops'
                    ? darkMode
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : darkMode
                      ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                      : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'workshops'
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'workshops'
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                Workshops
              </button>
              <button
                onClick={() => setActiveTab('conferences')}
                className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                  activeTab === 'conferences'
                    ? darkMode
                      ? 'bg-white text-[#00001a] shadow-xl'
                      : 'bg-[#00001a] text-white shadow-xl'
                    : darkMode
                      ? 'bg-[#00001a] text-white hover:bg-[#00001a]/80'
                      : 'bg-white text-[#00001a] hover:bg-[#00001a]/5'
                }`}
                style={{
                  boxShadow: activeTab === 'conferences'
                    ? darkMode
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                    : darkMode
                      ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                      : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'conferences'
                      ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                      : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = activeTab === 'conferences'
                      ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                      : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                Conferences
              </button>
            </div>
          </div>

          {/* Search Bars for different tabs */}
          {activeTab === 'all-communities' && (
            <div className={`p-4 rounded-lg border mb-6 ${
              darkMode
                ? 'bg-white/5 border-white/20'
                : 'bg-white border-[#00001a]/20'
            }`}>
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`} />
                  <input
                    type="text"
                    placeholder="Search by topic or community name..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'upcoming-events' && (
            <div className={`p-4 rounded-lg border mb-6 ${
              darkMode
                ? 'bg-white/5 border-white/20'
                : 'bg-white border-[#00001a]/20'
            }`}>
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`} />
                  <input
                    type="text"
                    placeholder="Search events by topic or keyword..."
                    value={eventsSearchQuery}
                    onChange={(e) => setEventsSearchQuery(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'workshops' && (
            <div className={`p-4 rounded-lg border mb-6 ${
              darkMode
                ? 'bg-white/5 border-white/20'
                : 'bg-white border-[#00001a]/20'
            }`}>
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`} />
                  <input
                    type="text"
                    placeholder="Search workshops by topic..."
                    value={workshopsSearchQuery}
                    onChange={(e) => setWorkshopsSearchQuery(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'conferences' && (
            <div className={`p-4 rounded-lg border mb-6 ${
              darkMode
                ? 'bg-white/5 border-white/20'
                : 'bg-white border-[#00001a]/20'
            }`}>
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                    darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                  }`} />
                  <input
                    type="text"
                    placeholder="Search conferences by topic..."
                    value={conferencesSearchQuery}
                    onChange={(e) => setConferencesSearchQuery(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Pseudo Name Requirement Check */}
        {!userpsudoname ? (
          <div className={`p-8 rounded-lg border text-center ${
            darkMode
              ? 'bg-white/5 border-white/20'
              : 'bg-white border-[#00001a]/20'
          }`}>
            <div className="flex flex-col items-center gap-4">
              <div className={`p-4 rounded-full ${
                darkMode
                  ? 'bg-orange-500/20'
                  : 'bg-orange-50'
              }`}>
                <AlertCircle size={32} className={darkMode ? 'text-orange-300' : 'text-orange-600'} />
              </div>
              <div>
                <h2 className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Pseudo Name Required
                </h2>
                <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Please create your pseudo name in profile settings to access community features.
                </p>
                <p className={`mt-2 text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  Your pseudo name will be used for all community interactions to maintain privacy.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Tab Content */}
            <div className="space-y-8">

              {/* My Communities Tab */}
              {activeTab === 'my-communities' && (
                <>
                  {/* Your Community Section */}
                  {userCommunity && (
            <div className={`p-6 rounded-lg border transition-all duration-500 shadow-xl ${
              darkMode
                ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
                : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_25px_rgba(0,0,26,0.2)]'
            }`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Your Community
                </h2>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setShowEditModal(true)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                    }`}
                    title="Edit Community Info"
                  >
                    <Edit size={16} />
                    <span className="text-sm">Edit</span>
                  </button>
                  <button
                    onClick={() => setShowDeleteModal(true)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-red-500/20 border-red-500/30 text-red-300 hover:bg-red-500/30'
                        : 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                    }`}
                    title="Delete Community"
                  >
                    <Trash2 size={16} />
                    <span className="text-sm">Delete</span>
                  </button>
                </div>
              </div>

              <div className="space-y-6">
                {/* Community Info */}
                <div className={`p-4 rounded-lg border ${
                  darkMode
                    ? 'bg-white/5 border-white/20'
                    : 'bg-gray-50 border-[#00001a]/10'
                }`}>
                  <h3 className={`font-bold text-xl mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    {userCommunity.name}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className={`font-semibold mb-2 ${darkMode ? 'text-white/90' : 'text-[#00001a]/90'}`}>
                        Purpose
                      </h4>
                      <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        {userCommunity.purpose}
                      </p>
                    </div>
                    <div>
                      <h4 className={`font-semibold mb-2 ${darkMode ? 'text-white/90' : 'text-[#00001a]/90'}`}>
                        Technology
                      </h4>
                      <span className={`inline-block px-3 py-1 text-sm rounded-full ${
                        darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                      }`}>
                        {userCommunity.technology}
                      </span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className={`font-semibold mb-2 ${darkMode ? 'text-white/90' : 'text-[#00001a]/90'}`}>
                      Scope
                    </h4>
                    <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      {userCommunity.scope}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-center gap-4">
                  <button
                    onClick={() => handleChatCommunity(userCommunity)}
                    className={`flex items-center gap-2 px-6 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                        : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                    }`}
                  >
                    <MessageCircle size={18} />
                    <span className="font-medium">Open Chat</span>
                  </button>
                  <button
                    onClick={() => handleCopyLink(userCommunity.name)}
                    className={`flex items-center gap-2 px-6 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                    }`}
                  >
                    <Link size={18} />
                    <span className="font-medium">Copy Link</span>
                  </button>
                </div>
              </div>
            </div>
                  )}

                  {/* No Communities Message for My Communities */}
                  {!userCommunity && (
                    <div className={`p-8 rounded-lg border text-center ${
                      darkMode
                        ? 'bg-white/5 border-white/20'
                        : 'bg-white border-[#00001a]/20'
                    }`}>
                      <div className="flex flex-col items-center gap-4">
                        <div className={`p-4 rounded-full ${
                          darkMode
                            ? 'bg-blue-500/20'
                            : 'bg-blue-50'
                        }`}>
                          <Users size={32} className={darkMode ? 'text-blue-300' : 'text-blue-600'} />
                        </div>
                        <div>
                          <h2 className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            No Communities Created
                          </h2>
                          <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            You haven't created any communities yet.
                          </p>
                          <p className={`mt-2 text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            Click "Request New Community" to create your first community.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Joined Communities Tab */}
              {activeTab === 'joined-communities' && (
                <div className={`p-6 rounded-lg border transition-all duration-500 shadow-xl ${
                  darkMode
                    ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
                    : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_25px_rgba(0,0,26,0.2)]'
                }`}>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Joined Communities
                    </h2>
                    <span className={`text-sm px-3 py-1 rounded-full ${
                      darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                    }`}>
                      {joinedCommunities.size} communities
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {allCommunities.filter(community => joinedCommunities.has(community.id)).map((community) => (
                      <div
                        key={community.id}
                        className={`p-6 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                            : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_15px_rgba(0,0,26,0.15)]'
                        }`}
                      >
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              {community.name}
                            </h3>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                            }`}>
                              {community.technology}
                            </span>
                          </div>
                          <p className={`text-sm mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            {community.purpose}
                          </p>
                          <p className={`text-xs mb-3 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            {community.scope}
                          </p>
                          <div className="flex items-center justify-between text-xs">
                            <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                              {community.members} members
                            </span>
                            <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                              by {community.creator}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleChatCommunity(community)}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 flex-1 justify-center ${
                              darkMode
                                ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                            }`}
                          >
                            <MessageCircle size={16} />
                            <span className="text-sm">Chat</span>
                          </button>
                          <button
                            onClick={() => handleJoinCommunity(community.id)}
                            disabled={isLoading}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                              isLoading
                                ? 'opacity-50 cursor-not-allowed'
                                : darkMode
                                  ? 'bg-red-500/20 border-red-500/30 text-red-300 hover:bg-red-500/30'
                                  : 'bg-white border-[#00001a]/20 text-[#00001a] hover:bg-[#00001a]/5'
                            }`}
                          >
                            <X size={16} />
                            <span className="text-sm">Leave</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {joinedCommunities.size === 0 && (
                    <div className="text-center py-12">
                      <div className="flex flex-col items-center gap-4">
                        <div className={`p-4 rounded-full ${
                          darkMode
                            ? 'bg-blue-500/20'
                            : 'bg-blue-50'
                        }`}>
                          <Users size={32} className={darkMode ? 'text-blue-300' : 'text-blue-600'} />
                        </div>
                        <div>
                          <h3 className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            No Joined Communities
                          </h3>
                          <p className={`text-lg ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            You haven't joined any communities yet.
                          </p>
                          <p className={`mt-2 text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            Browse "All Communities" to find and join communities.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* All Communities Tab */}
              {activeTab === 'all-communities' && (
                <div className={`p-6 rounded-lg border transition-all duration-500 shadow-xl ${
                  darkMode
                    ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
                    : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_25px_rgba(0,0,26,0.2)]'
                }`}>
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                All Communities
              </h2>
              <span className={`text-sm px-3 py-1 rounded-full ${
                darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
              }`}>
                {filteredCommunities.length} communities
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {(showAllCommunities ? filteredCommunities : filteredCommunities.slice(0, 6)).map((community) => {
                const isJoined = joinedCommunities.has(community.id)

                return (
                  <div
                    key={community.id}
                    className={`p-6 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                        : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_15px_rgba(0,0,26,0.15)]'
                    }`}
                  >
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          {community.name}
                        </h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                        }`}>
                          {community.technology}
                        </span>
                      </div>
                      <p className={`text-sm mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        {community.purpose}
                      </p>
                      <p className={`text-xs mb-3 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        {community.scope}
                      </p>
                      <div className="flex items-center justify-between text-xs">
                        <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                          {community.members} members
                        </span>
                        <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                          by {community.creator}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {!isJoined ? (
                        <>
                          <button
                            onClick={() => handleJoinCommunity(community.id)}
                            disabled={isLoading}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 flex-1 justify-center ${
                              isLoading
                                ? 'opacity-50 cursor-not-allowed'
                                : darkMode
                                  ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                  : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                            }`}
                          >
                            <UserPlus size={16} />
                            <span className="text-sm">Join</span>
                          </button>
                          <button
                            onClick={() => handleShowDetails(community, 'community')}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                              darkMode
                                ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                                : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                            }`}
                          >
                            <span className="text-sm">View Details</span>
                          </button>
                        </>
                      ) : (
                        <>
                          <button
                            onClick={() => handleChatCommunity(community)}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 flex-1 justify-center ${
                              darkMode
                                ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                            }`}
                          >
                            <MessageCircle size={16} />
                            <span className="text-sm">Chat</span>
                          </button>
                          <button
                            onClick={() => handleShowDetails(community, 'community')}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                              darkMode
                                ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                                : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                            }`}
                          >
                            <span className="text-sm">View Details</span>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* View More Button for Communities */}
            {filteredCommunities.length > 6 && (
              <div className="text-center mt-6">
                <button
                  onClick={() => setShowAllCommunities(!showAllCommunities)}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                      : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                  }`}
                >
                  {showAllCommunities ? 'View Less' : `View More (${filteredCommunities.length - 6} more)`}
                </button>
              </div>
            )}

                {filteredCommunities.length === 0 && (
                  <div className="text-center py-12">
                    <p className={`text-lg ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                      No communities found matching your search.
                    </p>
                  </div>
                )}
                </div>
              )}

              {/* Upcoming Events Tab */}
              {activeTab === 'upcoming-events' && (
                <div className={`p-6 rounded-lg border transition-all duration-500 shadow-xl ${
                  darkMode
                    ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
                    : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_25px_rgba(0,0,26,0.2)]'
                }`}>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Upcoming Events
                    </h2>
                    <span className={`text-sm px-3 py-1 rounded-full ${
                      darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                    }`}>
                      {filteredEvents.length} events
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {(showAllEvents ? filteredEvents : filteredEvents.slice(0, 4)).map((event) => {
                const isJoined = joinedEvents.has(event.id)

                return (
                  <div
                    key={event.id}
                    className={`p-6 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                        : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_15px_rgba(0,0,26,0.15)]'
                    }`}
                  >
                    <div className="mb-4">
                      <h3 className={`font-bold text-lg mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        {event.topic}
                      </h3>
                      <div className="flex items-center gap-2 mb-3">
                        <Users size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                        <span className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          {event.communityName}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm mb-3">
                        <div className="flex items-center gap-2">
                          <Calendar size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                          <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            {new Date(event.date).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                          <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            {event.time}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm mb-2">
                        <UserCheck size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                        <span className={`font-medium ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                          {event.attendees} Total Attendees
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleJoinEvent(event.id)}
                        disabled={isLoading}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 justify-center flex-1 ${
                          isLoading
                            ? 'opacity-50 cursor-not-allowed'
                            : isJoined
                              ? darkMode
                                ? 'bg-green-500/20 border-green-500/30 text-green-300 hover:bg-green-500/30'
                                : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                              : darkMode
                                ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                        }`}
                      >
                        {isJoined ? <Check size={16} /> : <UserCheck size={16} />}
                        <span className="text-sm">{isJoined ? 'Joined' : 'Join Event'}</span>
                      </button>
                      <button
                        onClick={() => handleShowDetails(event, 'event')}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                            : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                        }`}
                      >
                        <span className="text-sm">View Details</span>
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>

                  {/* View More Button for Events */}
                  {filteredEvents.length > 4 && (
                    <div className="text-center mt-6">
                      <button
                        onClick={() => setShowAllEvents(!showAllEvents)}
                        className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                            : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                        }`}
                      >
                        {showAllEvents ? 'View Less' : `View More (${filteredEvents.length - 4} more)`}
                      </button>
                    </div>
                  )}

                  {filteredEvents.length === 0 && (
                    <div className="text-center py-12">
                      <p className={`text-lg ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        No events found matching your search.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Workshops Tab */}
              {activeTab === 'workshops' && (
                <div className={`p-6 rounded-lg border transition-all duration-500 shadow-xl ${
                  darkMode
                    ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
                    : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_25px_rgba(0,0,26,0.2)]'
                }`}>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Workshops
                    </h2>
                    <span className={`text-sm px-3 py-1 rounded-full ${
                      darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                    }`}>
                      {filteredWorkshops.length} workshops
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {filteredWorkshops.map((workshop) => (
                      <div
                        key={workshop.id}
                        className={`p-6 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                            : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_15px_rgba(0,0,26,0.15)]'
                        }`}
                      >
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              {workshop.title}
                            </h3>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              workshop.type === 'Conference'
                                ? darkMode ? 'bg-purple-500/20 text-purple-300' : 'bg-purple-100 text-purple-600'
                                : darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                            }`}>
                              {workshop.type}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 mb-3">
                            <Users size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                            <span className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              {workshop.hostCommunity}
                            </span>
                          </div>

                          <p className={`text-sm mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            {workshop.description}
                          </p>

                          <div className="flex items-center gap-4 text-sm mb-3">
                            <div className="flex items-center gap-2">
                              <Calendar size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                              <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                                {new Date(workshop.date).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                              <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                                {workshop.time}
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 text-sm mb-3">
                            <UserCheck size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                            <span className={`font-medium ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                              {workshop.attendees} Registered
                            </span>
                          </div>

                          <div className="flex flex-wrap gap-1 mb-3">
                            {workshop.topics.slice(0, 3).map((topic, index) => (
                              <span
                                key={index}
                                className={`text-xs px-2 py-1 rounded-full ${
                                  darkMode ? 'bg-white/10 text-white/70' : 'bg-gray-100 text-gray-600'
                                }`}
                              >
                                {topic}
                              </span>
                            ))}
                            {workshop.topics.length > 3 && (
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                darkMode ? 'bg-white/10 text-white/70' : 'bg-gray-100 text-gray-600'
                              }`}>
                                +{workshop.topics.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleWorkshopRegistration(workshop.id, workshop.title)}
                            disabled={isLoading || !workshop.registrationOpen}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 flex-1 justify-center ${
                              isLoading || !workshop.registrationOpen
                                ? 'opacity-50 cursor-not-allowed'
                                : darkMode
                                  ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                  : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                            }`}
                          >
                            <UserPlus size={16} />
                            <span className="text-sm">
                              {workshop.registrationOpen ? 'Register' : 'Registration Closed'}
                            </span>
                          </button>
                          <button
                            onClick={() => handleShowDetails(workshop, 'workshop')}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                              darkMode
                                ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                                : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                            }`}
                          >
                            <span className="text-sm">View Details</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {filteredWorkshops.length === 0 && (
                    <div className="text-center py-12">
                      <p className={`text-lg ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        No workshops found matching your search.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Conferences Tab */}
              {activeTab === 'conferences' && (
                <div className={`p-6 rounded-lg border transition-all duration-500 shadow-xl ${
                  darkMode
                    ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_25px_rgba(59,130,246,0.4)]'
                    : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_25px_rgba(0,0,26,0.2)]'
                }`}>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Conferences
                    </h2>
                    <span className={`text-sm px-3 py-1 rounded-full ${
                      darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-[#00001a] text-white'
                    }`}>
                      {filteredConferences.length} conferences
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {filteredConferences.map((conference) => (
                      <div
                        key={conference.id}
                        className={`p-6 rounded-lg border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 border-white/20 hover:border-blue-400/50 hover:shadow-[0_0_15px_rgba(59,130,246,0.3)]'
                            : 'bg-white border-[#00001a]/20 hover:border-[#00001a]/30 hover:shadow-[0_0_15px_rgba(0,0,26,0.15)]'
                        }`}
                      >
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              {conference.title}
                            </h3>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              darkMode ? 'bg-purple-500/20 text-purple-300' : 'bg-[#00001a] text-white'
                            }`}>
                              {conference.type}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 mb-3">
                            <Users size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                            <span className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              {conference.hostCommunity}
                            </span>
                          </div>

                          <p className={`text-sm mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            {conference.description}
                          </p>

                          <div className="flex items-center gap-4 text-sm mb-3">
                            <div className="flex items-center gap-2">
                              <Calendar size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                              <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                                {new Date(conference.date).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                              <span className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                                {conference.time}
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 text-sm mb-3">
                            <UserCheck size={16} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                            <span className={`font-medium ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                              {conference.attendees} Registered
                            </span>
                          </div>

                          <div className="flex flex-wrap gap-1 mb-3">
                            {conference.topics.slice(0, 3).map((topic, index) => (
                              <span
                                key={index}
                                className={`text-xs px-2 py-1 rounded-full ${
                                  darkMode ? 'bg-white/10 text-white/70' : 'bg-gray-100 text-gray-600'
                                }`}
                              >
                                {topic}
                              </span>
                            ))}
                            {conference.topics.length > 3 && (
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                darkMode ? 'bg-white/10 text-white/70' : 'bg-gray-100 text-gray-600'
                              }`}>
                                +{conference.topics.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleWorkshopRegistration(conference.id, conference.title)}
                            disabled={isLoading || !conference.registrationOpen}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 flex-1 justify-center ${
                              isLoading || !conference.registrationOpen
                                ? 'opacity-50 cursor-not-allowed'
                                : darkMode
                                  ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                  : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                            }`}
                          >
                            <UserPlus size={16} />
                            <span className="text-sm">
                              {conference.registrationOpen ? 'Register' : 'Registration Closed'}
                            </span>
                          </button>
                          <button
                            onClick={() => handleShowDetails(conference, 'conference')}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                              darkMode
                                ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                                : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                            }`}
                          >
                            <span className="text-sm">View Details</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {filteredConferences.length === 0 && (
                    <div className="text-center py-12">
                      <p className={`text-lg ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        No conferences found matching your search.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
        </>
        )}

      </div>

      {/* Community Chat Modal */}
      {activeChatCommunity && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-4xl h-[600px] rounded-lg border shadow-2xl flex flex-col ${
            darkMode
              ? 'bg-gray-900/95 border-white/20'
              : 'bg-white border-gray-200'
          }`}>
            {/* Chat Header */}
            <div className="flex items-center justify-between p-4 border-b border-opacity-20">
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  {activeChatCommunity.name} Chat
                </h2>
                <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  {activeChatCommunity.technology} Community
                </p>
              </div>
              <button
                onClick={() => setActiveChatCommunity(null)}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  darkMode
                    ? 'hover:bg-white/10 text-white/70 hover:text-white'
                    : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                }`}
              >
                <X size={20} />
              </button>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {(chatMessages[activeChatCommunity.id] || []).map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender === userpsudoname ? 'justify-end' : 'justify-start'}`}
                >
                  <div className="relative">
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        message.type === 'file'
                          ? message.sender === userpsudoname
                            ? darkMode
                              ? 'bg-blue-500/10 text-blue-300 hover:bg-blue-500/20 border border-blue-500/30'
                              : 'bg-[#00001a]/10 text-[#00001a] hover:bg-[#00001a]/20 border border-[#00001a]/30'
                            : darkMode
                              ? 'bg-white/5 text-white hover:bg-white/10 border border-white/20'
                              : 'bg-gray-50 text-[#00001a] hover:bg-gray-100 border border-gray-200'
                          : message.sender === userpsudoname
                            ? darkMode
                              ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30'
                              : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                            : darkMode
                              ? 'bg-white/10 text-white hover:bg-white/20'
                              : 'bg-gray-100 text-[#00001a] hover:bg-gray-200'
                      }`}
                      onMouseEnter={() => setHoveredMessage(message.id)}
                      onMouseLeave={() => setHoveredMessage(null)}
                      onClick={() => setShowReactionPicker(showReactionPicker === message.id ? null : message.id)}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`text-xs font-medium ${
                          message.sender === userpsudoname
                            ? darkMode ? 'text-blue-200' : message.type === 'file' ? 'text-[#00001a]/80' : 'text-white/80'
                            : darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                        }`}>
                          {message.sender}
                        </span>
                        <span className={`text-xs ${
                          message.sender === userpsudoname
                            ? darkMode ? 'text-blue-200/60' : message.type === 'file' ? 'text-[#00001a]/60' : 'text-white/60'
                            : darkMode ? 'text-white/40' : 'text-[#00001a]/40'
                        }`}>
                          {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>

                      {/* Regular Text Message */}
                      {message.type !== 'file' && (
                        <p className="text-sm">{message.message}</p>
                      )}

                      {/* File Message */}
                      {message.type === 'file' && message.fileData && (
                        <div className="space-y-3">
                          {/* Smart Preview Based on File Type */}
                          {message.fileData.showPreview && (
                            <div className="relative">
                              {/* Image Preview (including GIFs) */}
                              {(message.fileData.isImage || message.fileData.isGif) && (
                                <div className="relative group">
                                  <img
                                    src={message.fileData.url}
                                    alt={message.fileData.name}
                                    className="max-w-full max-h-48 rounded-lg object-cover cursor-pointer transition-all duration-300 hover:opacity-90"
                                    style={{ maxWidth: '250px' }}
                                    title={message.fileData.isGif ? "GIF - Click to download" : "Image preview - Click to download"}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleDownloadFile(message.fileData)
                                    }}
                                  />
                                  {/* Download overlay for images */}
                                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        handleDownloadFile(message.fileData)
                                      }}
                                      className={`p-1.5 rounded-lg transition-all duration-300 ${
                                        darkMode
                                          ? 'bg-black/50 hover:bg-black/70 text-white'
                                          : 'bg-white/80 hover:bg-white text-[#00001a]'
                                      }`}
                                      title="Download image"
                                    >
                                      <Download size={14} />
                                    </button>
                                  </div>
                                </div>
                              )}

                              {/* Video Preview (short videos only) */}
                              {message.fileData.isVideo && message.fileData.showPreview && (
                                <div className="relative">
                                  <video
                                    src={message.fileData.url}
                                    controls
                                    className="max-w-full max-h-48 rounded-lg"
                                    style={{ maxWidth: '250px' }}
                                    preload="metadata"
                                    title="Video preview - Use controls to play"
                                  >
                                    Your browser does not support the video tag.
                                  </video>
                                  <div className="absolute top-2 right-2">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        handleDownloadFile(message.fileData)
                                      }}
                                      className={`p-1.5 rounded-lg transition-all duration-300 ${
                                        darkMode
                                          ? 'bg-black/50 hover:bg-black/70 text-white'
                                          : 'bg-white/80 hover:bg-white text-[#00001a]'
                                      }`}
                                      title="Download video"
                                    >
                                      <Download size={14} />
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* File Info - Always shown */}
                          <div className={`flex items-center gap-3 p-3 rounded-lg ${
                            darkMode ? 'bg-white/5' : 'bg-white/50'
                          }`}>
                            <div className="text-2xl">
                              {getFileIcon(message.fileData.name)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className={`text-sm font-medium truncate ${
                                darkMode ? 'text-white' : 'text-[#00001a]'
                              }`}>
                                {message.fileData.name}
                              </p>
                              <div className={`text-xs ${
                                darkMode ? 'text-white/60' : 'text-[#00001a]/60'
                              }`}>
                                <span>{formatFileSize(message.fileData.size)}</span>
                                {message.fileData.isVideo && message.fileData.duration > 0 && (
                                  <span> • {formatDuration(message.fileData.duration)}</span>
                                )}
                                {message.fileData.isDocument && <span> • Document</span>}
                                {message.fileData.isArchive && <span> • Archive</span>}
                                {message.fileData.isVideo && !message.fileData.showPreview && <span> • Large video</span>}
                              </div>
                            </div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDownloadFile(message.fileData)
                              }}
                              className={`p-2 rounded-lg transition-all duration-300 ${
                                darkMode
                                  ? 'bg-white/10 hover:bg-white/20 text-white'
                                  : 'bg-gray-200 hover:bg-gray-300 text-[#00001a]'
                              }`}
                              title={getFileTooltip(message.fileData)}
                            >
                              <Download size={16} />
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Reaction Picker */}
                    {(hoveredMessage === message.id || showReactionPicker === message.id) && (
                      <div className={`reaction-picker absolute ${
                        message.type === 'file' ? '-top-16' : '-top-12'
                      } left-1/2 transform -translate-x-1/2 z-10 ${
                        darkMode ? 'bg-gray-800/95' : 'bg-white/95'
                      } backdrop-blur-sm rounded-xl shadow-xl border ${
                        darkMode ? 'border-white/20' : 'border-gray-200'
                      } px-3 py-2 flex gap-1`}>
                        {reactionEmojis.map((emoji) => (
                          <button
                            key={emoji}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleReaction(message.id, emoji)
                            }}
                            className="text-lg hover:scale-125 transition-transform duration-200 p-1 rounded-lg hover:bg-black/10"
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Display Reactions */}
                    {messageReactions[message.id] && Object.keys(messageReactions[message.id]).length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {Object.entries(messageReactions[message.id]).map(([emoji, data]) => (
                          <button
                            key={emoji}
                            onClick={() => handleReaction(message.id, emoji)}
                            className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-all duration-200 ${
                              darkMode
                                ? 'bg-white/10 hover:bg-white/20 text-white border border-white/20'
                                : 'bg-gray-100 hover:bg-gray-200 text-[#00001a] border border-gray-300'
                            }`}
                          >
                            <span>{emoji}</span>
                            <span className="font-medium">{data.count}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Chat Input */}
            <div className="p-4 border-t border-opacity-20">
              <div className="flex items-center gap-3">
                <button
                  onClick={handleAttachFile}
                  className={`p-2 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
                      : 'bg-white border-[#00001a]/30 text-[#00001a] hover:bg-[#00001a]/5'
                  }`}
                  title="Attach file"
                >
                  <Paperclip size={16} />
                </button>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type your message..."
                  className={`flex-1 px-4 py-2 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                      : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                  } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                    darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                  }`}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  className={`p-2 rounded-lg border transition-all duration-300 ${
                    !newMessage.trim()
                      ? 'opacity-50 cursor-not-allowed'
                      : darkMode
                        ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                        : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                  }`}
                >
                  <Send size={16} />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Request Community Modal */}
      {showRequestModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-2xl rounded-lg border shadow-2xl ${
            darkMode
              ? 'bg-gray-900/95 border-white/20'
              : 'bg-white border-gray-200'
          }`}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Request New Community
                </h2>
                <button
                  onClick={() => setShowRequestModal(false)}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    darkMode
                      ? 'hover:bg-white/10 text-white/70 hover:text-white'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <X size={20} />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Community Name *
                  </label>
                  <input
                    type="text"
                    value={communityRequest.name}
                    onChange={(e) => handleRequestInputChange('name', e.target.value)}
                    placeholder="Enter community name..."
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Purpose *
                  </label>
                  <textarea
                    value={communityRequest.purpose}
                    onChange={(e) => handleRequestInputChange('purpose', e.target.value)}
                    placeholder="What is the purpose of this community?"
                    rows={3}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 resize-none ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Scope *
                  </label>
                  <textarea
                    value={communityRequest.scope}
                    onChange={(e) => handleRequestInputChange('scope', e.target.value)}
                    placeholder="What topics and activities will this community cover?"
                    rows={3}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 resize-none ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Technology *
                  </label>
                  <input
                    type="text"
                    value={communityRequest.technology}
                    onChange={(e) => handleRequestInputChange('technology', e.target.value)}
                    placeholder="Enter technologies (comma separated)"
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>
              </div>

              <div className="flex items-center justify-end gap-3 mt-8 pt-6 border-t border-opacity-20">
                <button
                  onClick={() => setShowRequestModal(false)}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'border-white/20 text-white/70 hover:bg-white/10 hover:text-white'
                      : 'border-[#00001a]/30 text-[#00001a]/70 hover:bg-[#00001a]/5 hover:text-[#00001a]'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleRequestCommunity}
                  disabled={isLoading || !communityRequest.name.trim() || !communityRequest.purpose.trim() || !communityRequest.scope.trim() || !communityRequest.technology.trim()}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    isLoading || !communityRequest.name.trim() || !communityRequest.purpose.trim() || !communityRequest.scope.trim() || !communityRequest.technology.trim()
                      ? 'opacity-50 cursor-not-allowed bg-gray-300 border-gray-300 text-gray-500'
                      : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                  }`}
                >
                  {isLoading ? 'Submitting...' : 'Submit Request'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Community Modal */}
      {showEditModal && userCommunity && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-2xl rounded-lg border shadow-2xl ${
            darkMode
              ? 'bg-gray-900/95 border-white/20'
              : 'bg-white border-gray-200'
          }`}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Edit Community Info
                </h2>
                <button
                  onClick={() => setShowEditModal(false)}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    darkMode
                      ? 'hover:bg-white/10 text-white/70 hover:text-white'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <X size={20} />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Community Name
                  </label>
                  <input
                    type="text"
                    defaultValue={userCommunity.name}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Purpose
                  </label>
                  <textarea
                    defaultValue={userCommunity.purpose}
                    rows={3}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 resize-none ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Scope
                  </label>
                  <textarea
                    defaultValue={userCommunity.scope}
                    rows={3}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 resize-none ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Technology
                  </label>
                  <select
                    defaultValue={userCommunity.technology}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 border-white/20 text-white focus:border-blue-400/50'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20 ${
                      darkMode ? 'focus:ring-blue-400' : 'focus:ring-[#00001a]'
                    }`}
                    style={darkMode ? {
                      colorScheme: 'dark',
                      color: 'white'
                    } : {
                      color: '#00001a'
                    }}
                  >
                    {techTopics.map(tech => (
                      <option key={tech} value={tech} style={darkMode ? {
                        backgroundColor: '#1a1a2e',
                        color: 'white'
                      } : {
                        backgroundColor: 'white',
                        color: '#00001a'
                      }}>{tech}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-end gap-3 mt-8 pt-6 border-t border-opacity-20">
                <button
                  onClick={() => setShowEditModal(false)}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'border-white/20 text-white/70 hover:bg-white/10 hover:text-white'
                      : 'border-[#00001a]/30 text-[#00001a]/70 hover:bg-[#00001a]/5 hover:text-[#00001a]'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleEditCommunity({})}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90`}
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Manage Members Modal */}
      {showMembersModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-2xl rounded-lg border shadow-2xl ${
            darkMode
              ? 'bg-gray-900/95 border-white/20'
              : 'bg-white border-gray-200'
          }`}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Manage Members
                </h2>
                <button
                  onClick={() => setShowMembersModal(false)}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    darkMode
                      ? 'hover:bg-white/10 text-white/70 hover:text-white'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <X size={20} />
                </button>
              </div>

              <div className="space-y-4">
                {mockMembers.map((member) => (
                  <div
                    key={member.id}
                    className={`p-4 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20'
                        : 'bg-white border-[#00001a]/20'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className={`font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          {member.name}
                        </h3>
                        <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                          {member.role} • Joined {new Date(member.joinDate).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handlePromoteMember(member.id)}
                          disabled={isLoading}
                          className={`px-3 py-1 text-xs rounded-lg border transition-all duration-300 ${
                            isLoading
                              ? 'opacity-50 cursor-not-allowed'
                              : darkMode
                                ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                                : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                          }`}
                        >
                          Promote
                        </button>
                        <button
                          onClick={() => handleRemoveMember(member.id)}
                          disabled={isLoading}
                          className={`px-3 py-1 text-xs rounded-lg border transition-all duration-300 ${
                            isLoading
                              ? 'opacity-50 cursor-not-allowed'
                              : darkMode
                                ? 'bg-red-500/20 border-red-500/30 text-red-300 hover:bg-red-500/30'
                                : 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                          }`}
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex items-center justify-end gap-3 mt-8 pt-6 border-t border-opacity-20">
                <button
                  onClick={() => setShowMembersModal(false)}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'border-white/20 text-white/70 hover:bg-white/10 hover:text-white'
                      : 'border-[#00001a]/30 text-[#00001a]/70 hover:bg-[#00001a]/5 hover:text-[#00001a]'
                  }`}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Community Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-md rounded-lg border shadow-2xl ${
            darkMode
              ? 'bg-gray-900/95 border-white/20'
              : 'bg-white border-gray-200'
          }`}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Delete Community
                </h2>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    darkMode
                      ? 'hover:bg-white/10 text-white/70 hover:text-white'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <X size={20} />
                </button>
              </div>

              <div className="mb-6">
                <p className={`${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Are you sure you want to delete this community? This action cannot be undone.
                </p>
              </div>

              <div className="flex items-center justify-end gap-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'border-white/20 text-white/70 hover:bg-white/10 hover:text-white'
                      : 'border-[#00001a]/30 text-[#00001a]/70 hover:bg-[#00001a]/5 hover:text-[#00001a]'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteCommunity}
                  className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'bg-red-500/20 border-red-500/30 text-red-300 hover:bg-red-500/30'
                      : 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                  }`}
                >
                  Delete Community
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Details Modal */}
      {showDetailsModal && detailsItem && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-lg border shadow-2xl ${
            darkMode
              ? 'bg-gray-900/95 border-white/20'
              : 'bg-white border-gray-200'
          }`}>
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  {detailsType === 'community' ? detailsItem.name :
                   detailsType === 'event' ? detailsItem.topic :
                   detailsItem.title} - Details
                </h2>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    darkMode
                      ? 'hover:bg-white/10 text-white/70 hover:text-white'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <X size={20} />
                </button>
              </div>

              {/* Modal Content */}
              <div className="space-y-6">
                {detailsType === 'community' && (
                  <>
                    <div className={`p-4 rounded-lg border ${
                      darkMode ? 'bg-white/5 border-white/20' : 'bg-gray-50 border-gray-200'
                    }`}>
                      <h3 className={`text-lg font-semibold mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Community Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Technology Focus
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.technology}
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Members
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.members} active members
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Created By
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.creator}
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Community Type
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            Public Community
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className={`p-4 rounded-lg border ${
                      darkMode ? 'bg-white/5 border-white/20' : 'bg-gray-50 border-gray-200'
                    }`}>
                      <h3 className={`text-lg font-semibold mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Purpose & Scope
                      </h3>
                      <p className={`mb-3 ${darkMode ? 'text-white/80' : 'text-[#00001a]/80'}`}>
                        <strong>Purpose:</strong> {detailsItem.purpose}
                      </p>
                      <p className={`${darkMode ? 'text-white/80' : 'text-[#00001a]/80'}`}>
                        <strong>Scope:</strong> {detailsItem.scope}
                      </p>
                    </div>
                  </>
                )}

                {detailsType === 'event' && (
                  <>
                    <div className={`p-4 rounded-lg border ${
                      darkMode ? 'bg-white/5 border-white/20' : 'bg-gray-50 border-gray-200'
                    }`}>
                      <h3 className={`text-lg font-semibold mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Event Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Date & Time
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {new Date(detailsItem.date).toLocaleDateString()} at {detailsItem.time}
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Community
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.communityName}
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Attendees
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.attendees} registered
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Event Type
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            Community Event
                          </p>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {(detailsType === 'workshop' || detailsType === 'conference') && (
                  <>
                    <div className={`p-4 rounded-lg border ${
                      darkMode ? 'bg-white/5 border-white/20' : 'bg-gray-50 border-gray-200'
                    }`}>
                      <h3 className={`text-lg font-semibold mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        {detailsType === 'workshop' ? 'Workshop' : 'Conference'} Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Date & Time
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {new Date(detailsItem.date).toLocaleDateString()} at {detailsItem.time}
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Host Community
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.hostCommunity}
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Attendees
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.attendees} registered
                          </p>
                        </div>
                        <div>
                          <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Registration Status
                          </p>
                          <p className={`${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {detailsItem.registrationOpen ? 'Open' : 'Closed'}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className={`p-4 rounded-lg border ${
                      darkMode ? 'bg-white/5 border-white/20' : 'bg-gray-50 border-gray-200'
                    }`}>
                      <h3 className={`text-lg font-semibold mb-3 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Description
                      </h3>
                      <p className={`mb-4 ${darkMode ? 'text-white/80' : 'text-[#00001a]/80'}`}>
                        {detailsItem.description}
                      </p>

                      <h4 className={`text-md font-semibold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Topics Covered
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {detailsItem.topics.map((topic, index) => (
                          <span
                            key={index}
                            className={`px-3 py-1 rounded-full text-sm ${
                              darkMode ? 'bg-white/10 text-white/70' : 'bg-gray-200 text-gray-700'
                            }`}
                          >
                            {topic}
                          </span>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* Action Buttons */}
                <div className="flex items-center justify-end gap-3 pt-4 border-t border-opacity-20">
                  <button
                    onClick={() => setShowDetailsModal(false)}
                    className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'border-white/20 text-white/70 hover:bg-white/10 hover:text-white'
                        : 'border-[#00001a]/30 text-[#00001a]/70 hover:bg-[#00001a]/5 hover:text-[#00001a]'
                    }`}
                  >
                    Close
                  </button>
                  {detailsType === 'community' && (
                    <button
                      onClick={() => {
                        setShowDetailsModal(false)
                        handleChatCommunity(detailsItem)
                      }}
                      className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                        darkMode
                          ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                          : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                      }`}
                    >
                      Join Chat
                    </button>
                  )}
                  {(detailsType === 'workshop' || detailsType === 'conference') && (
                    <button
                      onClick={() => {
                        setShowDetailsModal(false)
                        handleWorkshopRegistration(detailsItem.id, detailsItem.title)
                      }}
                      disabled={!detailsItem.registrationOpen}
                      className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                        !detailsItem.registrationOpen
                          ? 'opacity-50 cursor-not-allowed'
                          : darkMode
                            ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                            : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                      }`}
                    >
                      {detailsItem.registrationOpen ? 'Register Now' : 'Registration Closed'}
                    </button>
                  )}
                  {detailsType === 'event' && (
                    <button
                      onClick={() => {
                        setShowDetailsModal(false)
                        handleJoinEvent(detailsItem.id)
                      }}
                      className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                        darkMode
                          ? 'bg-blue-500/20 border-blue-500/30 text-blue-300 hover:bg-blue-500/30'
                          : 'bg-[#00001a] border-[#00001a] text-white hover:bg-[#00001a]/90'
                      }`}
                    >
                      Join Event
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Community
