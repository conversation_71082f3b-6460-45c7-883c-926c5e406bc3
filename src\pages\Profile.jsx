import React, { useState, useEffect, useRef } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { BarChart3, Users, Zap, Settings, User, Mail, Calendar, Lock, Smartphone, Pause, Trash2, Plus, Edit3, X, Save, ChevronDown, Clock, AlertTriangle, Trophy } from 'lucide-react'

// Custom Time Picker Component
const CustomTimePicker = ({ value, onChange, darkMode, className }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [hours, setHours] = useState('00')
  const [minutes, setMinutes] = useState('00')
  const [dropdownPosition, setDropdownPosition] = useState('bottom')
  const dropdownRef = useRef(null)
  const buttonRef = useRef(null)

  useEffect(() => {
    if (value) {
      const [h, m] = value.split(':')
      setHours(h || '00')
      setMinutes(m || '00')
    }
  }, [value])

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const calculateDropdownPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const dropdownHeight = 300 // Approximate height of dropdown

      // Check if there's enough space below
      if (rect.bottom + dropdownHeight > viewportHeight) {
        setDropdownPosition('top')
      } else {
        setDropdownPosition('bottom')
      }
    }
  }

  const handleTimeChange = (newHours, newMinutes) => {
    const timeString = `${newHours.padStart(2, '0')}:${newMinutes.padStart(2, '0')}`
    setHours(newHours.padStart(2, '0'))
    setMinutes(newMinutes.padStart(2, '0'))
    onChange(timeString)
    setIsOpen(false)
  }

  const handleToggle = () => {
    if (!isOpen) {
      calculateDropdownPosition()
    }
    setIsOpen(!isOpen)
  }

  const generateHours = () => Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'))
  const generateMinutes = () => Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'))

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        ref={buttonRef}
        type="button"
        onClick={handleToggle}
        className={`${className} flex items-center justify-between cursor-pointer`}
      >
        <span>{hours}:{minutes}</span>
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div
          className={`absolute ${dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'} left-0 z-[9999] rounded-lg border shadow-xl ${
            darkMode
              ? 'bg-gray-800 border-white/10 shadow-black/20'
              : 'bg-white border-gray-200 shadow-gray-300/50'
          }`}
          style={{
            width: '200px',
            maxHeight: '280px',
            position: 'absolute',
            zIndex: 9999
          }}
        >
          <div className="flex">
            {/* Hours Column */}
            <div className={`flex-1 border-r ${darkMode ? 'border-white/10' : 'border-gray-200'}`}>
              <div className={`p-2 text-xs font-medium text-center border-b ${
                darkMode ? 'border-white/10 text-white' : 'border-gray-200 text-[#00001a]'
              }`}>
                Hours
              </div>
              <div className="max-h-44 overflow-y-auto">
                {generateHours().map((hour) => (
                  <button
                    key={hour}
                    type="button"
                    onClick={() => handleTimeChange(hour, minutes)}
                    className={`w-full px-3 py-2 text-sm text-left hover:bg-[#00001a] hover:text-white transition-colors ${
                      hours === hour
                        ? 'bg-[#00001a] text-white'
                        : darkMode
                          ? 'text-white hover:bg-[#00001a]'
                          : 'text-[#00001a] hover:bg-[#00001a] hover:text-white'
                    }`}
                  >
                    {hour}
                  </button>
                ))}
              </div>
            </div>

            {/* Minutes Column */}
            <div className="flex-1">
              <div className={`p-2 text-xs font-medium text-center border-b ${
                darkMode ? 'border-white/10 text-white' : 'border-gray-200 text-[#00001a]'
              }`}>
                Minutes
              </div>
              <div className="max-h-44 overflow-y-auto">
                {generateMinutes().map((minute) => (
                  <button
                    key={minute}
                    type="button"
                    onClick={() => handleTimeChange(hours, minute)}
                    className={`w-full px-3 py-2 text-sm text-left hover:bg-[#00001a] hover:text-white transition-colors ${
                      minutes === minute
                        ? 'bg-[#00001a] text-white'
                        : darkMode
                          ? 'text-white hover:bg-[#00001a]'
                          : 'text-[#00001a] hover:bg-[#00001a] hover:text-white'
                    }`}
                  >
                    {minute}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

const Profile = ({ darkMode }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [activeTab, setActiveTab] = useState('Profile')

  // Handle URL parameters for tab navigation
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const tabParam = searchParams.get('tab')
    if (tabParam && ['Profile', 'Expertise', 'Availability', 'Payment', 'Settings'].includes(tabParam)) {
      setActiveTab(tabParam)
    }
  }, [location.search])
  const [profileData, setProfileData] = useState({
    firstName: 'pred',
    lastName: '',
    email: '<EMAIL>',
    phone: '',
    country: '',
    city: '',
    gender: '',
    dob: '',
    bio: '',
    website: '',
    github: '',
    linkedin: '',
    profilePic: null,
    languages: [],
    skills: [],
    projects: [],
    experience: [],
    achievements: [],
    bankAccount: {
      accountNumber: '',
      routingNumber: '',
      bankName: ''
    }
  })

  // Pseudoname state management
  const [pseudoname, setPseudoname] = useState('')
  const [savedPseudoname, setSavedPseudoname] = useState('')
  const [autoGeneratedpsudoname, setAutoGeneratedpsudoname] = useState('')
  const [isPseudonameSet, setIsPseudonameSet] = useState(false)
  const [useCustomName, setUseCustomName] = useState(false)

  // Price customization state
  const [customPrice, setCustomPrice] = useState('')
  const [savedPrice, setSavedPrice] = useState('')
  const [priceError, setPriceError] = useState('')
  const [hasPricingBadge, setHasPricingBadge] = useState(false) // Badge requirement for price editing
  const fixedPricePerMinute = 5
  const minCustomPrice = 5
  const maxCustomPrice = 10

  const [followers] = useState(1234)
  const [interests, setInterests] = useState(['Programming', 'AI/ML', 'Web Development', 'Teaching'])
  const [profileCompletion, setProfileCompletion] = useState(75)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [showBankModal, setShowBankModal] = useState(false)
  const [show2FAModal, setShow2FAModal] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showDeactivateConfirm, setShowDeactivateConfirm] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showTransactionModal, setShowTransactionModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [showPaymentPrefsModal, setShowPaymentPrefsModal] = useState(false)
  const [saveStatus, setSaveStatus] = useState('') // 'saving', 'saved', 'error'
  const [availabilitySaveStatus, setAvailabilitySaveStatus] = useState('') // 'saving', 'saved', 'error'
  const [profilePicture, setProfilePicture] = useState(null) // For storing profile picture
  const [uploadingPicture, setUploadingPicture] = useState(false) // Upload status
  const [weeklySchedule, setWeeklySchedule] = useState({
    monday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    tuesday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    wednesday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    thursday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    friday: { enabled: true, startTime: '09:00', endTime: '17:00' },
    saturday: { enabled: false, startTime: '10:00', endTime: '16:00' },
    sunday: { enabled: false, startTime: '10:00', endTime: '16:00' }
  })
  const [podcastSchedule, setPodcastSchedule] = useState({
    monday: { enabled: true, startTime: '10:00', endTime: '16:00' },
    tuesday: { enabled: true, startTime: '10:00', endTime: '16:00' },
    wednesday: { enabled: true, startTime: '10:00', endTime: '16:00' },
    thursday: { enabled: true, startTime: '10:00', endTime: '16:00' },
    friday: { enabled: true, startTime: '10:00', endTime: '16:00' },
    saturday: { enabled: false, startTime: '10:00', endTime: '16:00' },
    sunday: { enabled: false, startTime: '10:00', endTime: '16:00' }
  })
  const [selectedTimeZone, setSelectedTimeZone] = useState('UTC-5')
  const [showTimeZoneModal, setShowTimeZoneModal] = useState(false)

  // Load psudoname data from localStorage and generate auto psudoname on component mount
  useEffect(() => {
    // Load saved psudoname data
    const savedData = localStorage.getItem('userPseudonym')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        setSavedPseudoname(parsed.pseudonym)
        setIsPseudonameSet(parsed.isSet)
        setUseCustomName(parsed.isCustom || false)
      } catch (error) {
        console.error('Error parsing saved pseudonym:', error)
      }
    }

    // Load saved price data
    const savedPriceData = localStorage.getItem('userCustomPrice')
    if (savedPriceData) {
      try {
        const parsed = JSON.parse(savedPriceData)
        setSavedPrice(parsed.price)
        setCustomPrice(parsed.price)
      } catch (error) {
        console.error('Error parsing saved price:', error)
      }
    }

    // Load badge data (for demo purposes, you can set this to true to test)
    const badgeData = localStorage.getItem('userPricingBadge')
    if (badgeData) {
      try {
        const parsed = JSON.parse(badgeData)
        setHasPricingBadge(parsed.hasBadge)
      } catch (error) {
        console.error('Error parsing badge data:', error)
      }
    }

    // Generate auto psudoname if not already generated
    if (!autoGeneratedpsudoname) {
      const adjectives = ['Swift', 'Clever', 'Bright', 'Sharp', 'Quick', 'Smart', 'Wise', 'Bold', 'Keen', 'Agile']
      const animals = ['Falcon', 'Eagle', 'Wolf', 'Fox', 'Tiger', 'Lion', 'Hawk', 'Raven', 'Lynx', 'Panther']
      const randomNum = Math.floor(Math.random() * 999) + 1
      const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
      const animal = animals[Math.floor(Math.random() * animals.length)]
      const generatedName = `${adjective}${animal}${randomNum}`
      setAutoGeneratedpsudoname(generatedName)
    }
  }, [autoGeneratedpsudoname])

  // Pseudoname functions
  const regenerateAutopsudoname = () => {
    if (isPseudonameSet) return // Don't allow regeneration if already set
    const adjectives = ['Swift', 'Clever', 'Bright', 'Sharp', 'Quick', 'Smart', 'Wise', 'Bold', 'Keen', 'Agile']
    const animals = ['Falcon', 'Eagle', 'Wolf', 'Fox', 'Tiger', 'Lion', 'Hawk', 'Raven', 'Lynx', 'Panther']
    const randomNum = Math.floor(Math.random() * 999) + 1
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
    const animal = animals[Math.floor(Math.random() * animals.length)]
    setAutoGeneratedpsudoname(`${adjective}${animal}${randomNum}`)
  }

  const savepsudoname = () => {
    if (isPseudonameSet) return // Don't allow changes if already set

    let nameToSave
    if (useCustomName) {
      nameToSave = pseudoname.trim()
      if (!nameToSave) {
        alert('Please enter a pseudo name')
        return
      }
    } else {
      nameToSave = autoGeneratedpsudoname
    }

    setSavedPseudoname(nameToSave)
    setIsPseudonameSet(true)

    // Save to localStorage
    localStorage.setItem('userPseudonym', JSON.stringify({
      pseudonym: nameToSave,
      isSet: true,
      isCustom: useCustomName
    }))

    setSaveStatus('saved')
    setTimeout(() => setSaveStatus(''), 3000)

    // Show confirmation
    alert(`Pseudo name "${nameToSave}" has been set successfully! This cannot be changed later.`)
  }

  const handlePseudonameKeyPress = (e) => {
    if (e.key === 'Enter' && !isPseudonameSet) {
      savepsudoname()
    }
  }

  // Price validation function
  const handlePriceChange = (value) => {
    if (!hasPricingBadge) return // Don't allow changes without badge

    setCustomPrice(value)
    if (value === '') {
      setPriceError('')
      return
    }

    const numValue = parseFloat(value)
    if (isNaN(numValue) || numValue <= 0) {
      setPriceError('Please enter a valid price')
    } else if (numValue < minCustomPrice) {
      setPriceError(`Price must be at least ${minCustomPrice}/min`)
    } else if (numValue > maxCustomPrice) {
      setPriceError(`Price must not exceed ${maxCustomPrice}/min`)
    } else {
      setPriceError('')
    }
  }

  // Save price function
  const saveCustomPrice = () => {
    if (!hasPricingBadge) {
      alert('You need to earn the required badge to set custom pricing')
      return
    }

    if (priceError || !customPrice) {
      alert('Please enter a valid price before saving')
      return
    }

    const numValue = parseFloat(customPrice)
    if (numValue < minCustomPrice || numValue > maxCustomPrice) {
      alert(`Price must be between ${minCustomPrice} and ${maxCustomPrice}/min`)
      return
    }

    setSavedPrice(customPrice)

    // Save to localStorage
    localStorage.setItem('userCustomPrice', JSON.stringify({
      price: customPrice,
      savedAt: new Date().toISOString()
    }))

    alert(`Custom price ${customPrice}/min has been saved successfully!`)
  }

  // Get current effective price
  const getCurrentPrice = () => {
    return savedPrice || fixedPricePerMinute
  }

  // Toggle badge for testing (you can remove this in production)
  const togglePricingBadge = () => {
    const newBadgeStatus = !hasPricingBadge
    setHasPricingBadge(newBadgeStatus)
    localStorage.setItem('userPricingBadge', JSON.stringify({
      hasBadge: newBadgeStatus,
      earnedAt: new Date().toISOString()
    }))

    if (!newBadgeStatus) {
      // Clear price when badge is removed
      setCustomPrice('')
      setPriceError('')
    }
  }

  const getDisplaypsudoname = () => {
    return savedPseudoname || autoGeneratedpsudoname
  }

  const hasUnsavedPseudonameChanges = () => {
    if (isPseudonameSet) return false
    const currentInput = pseudoname.trim() || autoGeneratedpsudoname
    return currentInput !== savedPseudoname
  }

  // Payment-related state
  const [paymentMethods, setPaymentMethods] = useState([
    { id: 1, type: 'UPI', name: 'Google Pay', identifier: 'pred@okaxis', isDefault: true },
    { id: 2, type: 'Bank', name: 'HDFC Bank', identifier: '****1234', isDefault: false },
    { id: 3, type: 'Stripe', name: 'Stripe Connect', identifier: 'acct_****5678', isDefault: false }
  ])
  const [transactions, setTransactions] = useState([
    { id: 1, date: '2024-01-15', amount: 250.00, type: 'Earning', description: 'Session with John Doe', status: 'Completed' },
    { id: 2, date: '2024-01-14', amount: 180.00, type: 'Earning', description: 'Tutorial Session', status: 'Completed' },
    { id: 3, date: '2024-01-13', amount: 500.00, type: 'Withdrawal', description: 'Bank Transfer', status: 'Pending' },
    { id: 4, date: '2024-01-12', amount: 320.00, type: 'Earning', description: 'Consultation Call', status: 'Completed' },
    { id: 5, date: '2024-01-11', amount: 150.00, type: 'Earning', description: 'Code Review', status: 'Completed' }
  ])
  const [paymentPreferences, setPaymentPreferences] = useState({
    autoWithdraw: false,
    withdrawalThreshold: 1000,
    frequency: 'weekly',
    paymentPin: '****'
  })

  // Project management state
  const [projects, setProjects] = useState([
    {
      id: 1,
      title: 'E-commerce Platform',
      description: 'Full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment processing, and admin dashboard.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'JWT'],
      link: 'https://github.com/username/ecommerce-platform',
      status: 'Completed',
      duration: '3 months'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'Collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
      technologies: ['Vue.js', 'Express.js', 'PostgreSQL', 'Socket.io'],
      link: 'https://github.com/username/task-manager',
      status: 'In Progress',
      duration: '2 months'
    },
    {
      id: 3,
      title: 'Data Analytics Dashboard',
      description: 'Interactive dashboard for data visualization and analytics with charts, filters, and real-time data processing.',
      technologies: ['React', 'D3.js', 'Python', 'Flask', 'Redis'],
      link: 'https://github.com/username/analytics-dashboard',
      status: 'Completed',
      duration: '4 months'
    }
  ])
  const [showAddProjectModal, setShowAddProjectModal] = useState(false)
  const [showEditProjectModal, setShowEditProjectModal] = useState(false)
  const [editingProject, setEditingProject] = useState(null)
  const [newProject, setNewProject] = useState({
    title: '',
    description: '',
    technologies: [],
    link: '',
    status: 'In Progress',
    duration: ''
  })
  const [newTechnology, setNewTechnology] = useState('')

  const tabs = ['Profile', 'Expertise', 'Availability', 'Payment', 'Settings']
  const availableInterests = ['Programming', 'AI/ML', 'Web Development', 'Teaching', 'Data Science', 'Mobile Development', 'DevOps', 'UI/UX Design', 'Blockchain', 'Cybersecurity']
  const languages = ['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Portuguese', 'Russian', 'Arabic', 'Hindi']
  const skillCategories = ['Frontend Development', 'Backend Development', 'Mobile Development', 'Data Science', 'Machine Learning', 'DevOps', 'UI/UX Design', 'Database Management']
  const timeZones = [
    { value: 'UTC-12', label: 'UTC-12 (Baker Island)' },
    { value: 'UTC-11', label: 'UTC-11 (American Samoa)' },
    { value: 'UTC-10', label: 'UTC-10 (Hawaii)' },
    { value: 'UTC-9', label: 'UTC-9 (Alaska)' },
    { value: 'UTC-8', label: 'UTC-8 (Pacific Time)' },
    { value: 'UTC-7', label: 'UTC-7 (Mountain Time)' },
    { value: 'UTC-6', label: 'UTC-6 (Central Time)' },
    { value: 'UTC-5', label: 'UTC-5 (Eastern Time)' },
    { value: 'UTC-4', label: 'UTC-4 (Atlantic Time)' },
    { value: 'UTC-3', label: 'UTC-3 (Argentina)' },
    { value: 'UTC-2', label: 'UTC-2 (South Georgia)' },
    { value: 'UTC-1', label: 'UTC-1 (Azores)' },
    { value: 'UTC+0', label: 'UTC+0 (London, Dublin)' },
    { value: 'UTC+1', label: 'UTC+1 (Paris, Berlin)' },
    { value: 'UTC+2', label: 'UTC+2 (Cairo, Athens)' },
    { value: 'UTC+3', label: 'UTC+3 (Moscow, Istanbul)' },
    { value: 'UTC+4', label: 'UTC+4 (Dubai, Baku)' },
    { value: 'UTC+5', label: 'UTC+5 (Karachi, Tashkent)' },
    { value: 'UTC+6', label: 'UTC+6 (Dhaka, Almaty)' },
    { value: 'UTC+7', label: 'UTC+7 (Bangkok, Jakarta)' },
    { value: 'UTC+8', label: 'UTC+8 (Beijing, Singapore)' },
    { value: 'UTC+9', label: 'UTC+9 (Tokyo, Seoul)' },
    { value: 'UTC+10', label: 'UTC+10 (Sydney, Melbourne)' },
    { value: 'UTC+11', label: 'UTC+11 (Solomon Islands)' },
    { value: 'UTC+12', label: 'UTC+12 (New Zealand)' }
  ]
  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

  const toggleInterest = (interest) => {
    setInterests(prev =>
      prev.includes(interest)
        ? prev.filter(i => i !== interest)
        : [...prev, interest]
    )
  }

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNestedInputChange = (parent, field, value) => {
    setProfileData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }))
  }

  // Total sessions data (this would typically come from an API)
  const totalSessions = 127

  // Button handlers
  const handleSaveProfile = async () => {
    console.log('🔥 Save Changes button clicked!')
    setSaveStatus('saving')
    try {
      console.log('💾 Saving profile data:', profileData)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Here you would make actual API call:
      // const response = await fetch('/api/profile', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ ...profileData, interests })
      // })

      setSaveStatus('saved')
      setTimeout(() => setSaveStatus(''), 3000)
    } catch (error) {
      console.error('Error saving profile:', error)
      setSaveStatus('error')
      setTimeout(() => setSaveStatus(''), 3000)
    }
  }

  const handleChangePassword = () => {
    setShowPasswordModal(true)
  }

  const handleTwoFactorAuth = () => {
    setShow2FAModal(true)
  }

  // Profile picture upload handler
  const handleProfilePictureUpload = (file) => {
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      setUploadingPicture(true);

      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        // Simulate upload delay
        setTimeout(() => {
          setProfilePicture(e.target.result);
          setUploadingPicture(false);
          console.log('✅ Profile picture uploaded successfully:', file.name);

          // Here you would typically upload to your server
          // For now, we'll just show a success message
          setSaveStatus('saved');
          setTimeout(() => setSaveStatus(''), 3000);
        }, 1000);
      };
      reader.readAsDataURL(file);
    }
  }

  const handleAddBankAccount = () => {
    setShowBankModal(true)
  }

  // Payment handlers
  const handleAddPaymentMethod = () => {
    setShowPaymentModal(true)
  }

  const handleViewTransactions = () => {
    navigate('/payments')
  }

  const handleLoadMoreTransactions = () => {
    // Simulate loading more transactions
    const newTransactions = [
      { id: transactions.length + 1, date: '2024-01-10', amount: 200.00, type: 'Earning', description: 'Debug Session', status: 'Completed' },
      { id: transactions.length + 2, date: '2024-01-09', amount: 300.00, type: 'Earning', description: 'Project Review', status: 'Completed' }
    ]
    setTransactions(prev => [...prev, ...newTransactions])
  }

  const handleExportPaymentData = () => {
    setShowExportModal(true)
  }

  const handleExportFormat = (format) => {
    // Simulate export functionality
    const data = transactions.map(t => ({
      Date: t.date,
      Amount: `$${t.amount}`,
      Type: t.type,
      Description: t.description,
      Status: t.status
    }))

    if (format === 'csv') {
      const csv = [
        Object.keys(data[0]).join(','),
        ...data.map(row => Object.values(row).join(','))
      ].join('\n')

      const blob = new Blob([csv], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'payment_data.csv'
      a.click()
    } else if (format === 'pdf') {
      alert('PDF export functionality would be implemented here')
    }
    setShowExportModal(false)
  }

  const handlePaymentPreferences = () => {
    setShowPaymentPrefsModal(true)
  }

  const handleUpdatePaymentPrefs = (newPrefs) => {
    setPaymentPreferences(prev => ({ ...prev, ...newPrefs }))
  }

  const handleDeactivateAccount = () => {
    setShowDeactivateConfirm(true)
  }

  const handleDeleteAccount = () => {
    setShowDeleteConfirm(true)
  }

  const handleAccountSettings = () => {
    setActiveTab('Settings')
  }

  // Availability handlers
  const handleScheduleToggle = (day) => {
    setWeeklySchedule(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        enabled: !prev[day].enabled
      }
    }))
  }

  const handleTimeChange = (day, timeType, value) => {
    setWeeklySchedule(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [timeType]: value
      }
    }))
  }

  // Podcast availability handlers
  const handlePodcastScheduleToggle = (day) => {
    setPodcastSchedule(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        enabled: !prev[day].enabled
      }
    }))
  }

  const handlePodcastTimeChange = (day, timeType, value) => {
    setPodcastSchedule(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [timeType]: value
      }
    }))
  }

  const handleTimeZoneChange = (timezone) => {
    setSelectedTimeZone(timezone)
    setShowTimeZoneModal(false)
    console.log('🌍 Timezone changed to:', timezone)
  }

  const handleSaveAvailability = async () => {
    setAvailabilitySaveStatus('saving')
    try {
      console.log('📅 Saving availability settings:', { weeklySchedule, podcastSchedule, selectedTimeZone })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Here you would make actual API call:
      // const response = await fetch('/api/availability', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ weeklySchedule, podcastSchedule, selectedTimeZone })
      // })

      setAvailabilitySaveStatus('saved')
      setTimeout(() => setAvailabilitySaveStatus(''), 3000)
    } catch (error) {
      console.error('Error saving availability:', error)
      setAvailabilitySaveStatus('error')
      setTimeout(() => setAvailabilitySaveStatus(''), 3000)
    }
  }

  // Confirmation handlers
  const confirmDeactivateAccount = async () => {
    try {
      console.log('⏸️ Deactivating account...')
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Here you would make actual API call:
      // await fetch('/api/account/deactivate', { method: 'POST' })

      alert('Account has been deactivated successfully.')
      setShowDeactivateConfirm(false)
    } catch (error) {
      console.error('Error deactivating account:', error)
      alert('Failed to deactivate account. Please try again.')
    }
  }

  const confirmDeleteAccount = async () => {
    try {
      console.log('🗑️ Deleting account...')
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Here you would make actual API call:
      // await fetch('/api/account/delete', { method: 'DELETE' })

      alert('Account has been deleted successfully.')
      setShowDeleteConfirm(false)
      // Redirect to login or home page
    } catch (error) {
      console.error('Error deleting account:', error)
      alert('Failed to delete account. Please try again.')
    }
  }

  // Project management handlers
  const handleAddProject = () => {
    setNewProject({
      title: '',
      description: '',
      technologies: [],
      link: '',
      status: 'In Progress',
      duration: ''
    })
    setNewTechnology('')
    setShowAddProjectModal(true)
  }

  const handleEditProject = (project) => {
    setEditingProject({ ...project })
    setNewTechnology('')
    setShowEditProjectModal(true)
  }

  const handleDeleteProject = (projectId) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      setProjects(prev => prev.filter(p => p.id !== projectId))
      console.log('🗑️ Project deleted:', projectId)
    }
  }

  const handleSaveNewProject = () => {
    if (!newProject.title.trim() || !newProject.description.trim()) {
      alert('Please fill in the title and description.')
      return
    }

    const project = {
      ...newProject,
      id: Date.now() // Simple ID generation
    }

    setProjects(prev => [...prev, project])
    setShowAddProjectModal(false)
    console.log('💾 New project added:', project)
  }

  const handleSaveEditProject = () => {
    if (!editingProject.title.trim() || !editingProject.description.trim()) {
      alert('Please fill in the title and description.')
      return
    }

    setProjects(prev => prev.map(p =>
      p.id === editingProject.id ? editingProject : p
    ))
    setShowEditProjectModal(false)
    setEditingProject(null)
    console.log('💾 Project updated:', editingProject)
  }

  const handleAddTechnology = (isEditing = false) => {
    if (!newTechnology.trim()) return

    if (isEditing) {
      if (!editingProject.technologies.includes(newTechnology.trim())) {
        setEditingProject(prev => ({
          ...prev,
          technologies: [...prev.technologies, newTechnology.trim()]
        }))
      }
    } else {
      if (!newProject.technologies.includes(newTechnology.trim())) {
        setNewProject(prev => ({
          ...prev,
          technologies: [...prev.technologies, newTechnology.trim()]
        }))
      }
    }
    setNewTechnology('')
  }

  const handleRemoveTechnology = (tech, isEditing = false) => {
    if (isEditing) {
      setEditingProject(prev => ({
        ...prev,
        technologies: prev.technologies.filter(t => t !== tech)
      }))
    } else {
      setNewProject(prev => ({
        ...prev,
        technologies: prev.technologies.filter(t => t !== tech)
      }))
    }
  }

  return (
    <>
      <style>
        {`
          /* Global time picker styling */
          * {
            --time-picker-accent: #00001a;
          }

          /* Override browser default time picker colors */
          input[type="time"] {
            accent-color: #00001a !important;
            color-scheme: ${darkMode ? 'dark' : 'light'};
            -webkit-appearance: none;
            -moz-appearance: textfield;
          }

          input[type="time"]::-webkit-calendar-picker-indicator {
            filter: ${darkMode ? 'invert(1)' : 'invert(0)'};
            background-color: transparent !important;
          }

          /* Target the time picker dropdown - more aggressive approach */
          input[type="time"]::-webkit-datetime-edit-hour-field:focus,
          input[type="time"]::-webkit-datetime-edit-minute-field:focus,
          input[type="time"]::-webkit-datetime-edit-ampm-field:focus {
            background-color: #00001a !important;
            color: white !important;
            outline: none !important;
            border-radius: 4px !important;
            box-shadow: 0 0 0 2px #00001a !important;
          }

          input[type="time"]::-webkit-datetime-edit-hour-field:hover,
          input[type="time"]::-webkit-datetime-edit-minute-field:hover,
          input[type="time"]::-webkit-datetime-edit-ampm-field:hover {
            background-color: #00001a !important;
            color: white !important;
          }

          input[type="time"]::-webkit-datetime-edit-hour-field,
          input[type="time"]::-webkit-datetime-edit-minute-field,
          input[type="time"]::-webkit-datetime-edit-ampm-field {
            padding: 2px 4px !important;
            border-radius: 4px !important;
            background-color: transparent !important;
          }

          /* Try to override the dropdown list */
          input[type="time"]::-webkit-list-button {
            background-color: #00001a !important;
            color: white !important;
          }

          /* Additional targeting for Chrome/Safari time picker */
          input[type="time"]::-webkit-inner-spin-button {
            background-color: #00001a !important;
          }

          /* Target the actual dropdown list items */
          input[type="time"] option {
            background-color: #00001a !important;
            color: white !important;
          }

          input[type="time"] option:checked,
          input[type="time"] option:selected {
            background-color: #00001a !important;
            color: white !important;
          }

          /* Try to target the time picker popup */
          input[type="time"]::-webkit-datetime-edit {
            color: inherit !important;
          }

          /* Force accent color on all form controls */
          input, select, button {
            accent-color: #00001a !important;
          }
        `}
      </style>
      <div className={`h-full w-full transition-all duration-500 ${
        darkMode ? 'bg-[#00001a]' : 'bg-gray-50'
      }`}>
      <div className="p-6 space-y-6">
        {/* Profile Header */}
        <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 ${
          darkMode
            ? 'bg-white/3 border-white/20'
            : 'bg-white border-gray-200'
        }`}>
          {/* Header with Personal Information title and Edit Button */}
          <div className="flex items-center justify-between mb-6">
            <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Personal Information
            </h3>
            <button
              onClick={() => {
                console.log('Edit Profile clicked');
                // You can add edit functionality here
                alert('Edit Profile functionality would be implemented here');
              }}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
              darkMode
                ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
            }`}>
              <Edit3 size={16} />
              Edit Profile
            </button>
          </div>

          {/* Profile Completion */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                Profile Completion
              </span>
              <span className={`text-sm font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                75%
              </span>
            </div>
            <div className={`w-full rounded-full h-2 ${darkMode ? 'bg-white/10' : 'bg-gray-200'}`}>
              <div className={`h-2 rounded-full ${darkMode ? 'bg-white' : 'bg-[#00001a]'}`} style={{width: '75%'}}></div>
            </div>
          </div>



          {/* Profile Info */}
          <div className="flex items-center gap-4">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold overflow-hidden relative ${
              darkMode ? 'bg-white/10 text-white' : 'bg-gray-200 text-[#00001a]'
            }`}>
              {uploadingPicture ? (
                <div className="w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
              ) : profilePicture ? (
                <img
                  src={profilePicture}
                  alt="Profile"
                  className="w-full h-full object-cover rounded-full"
                />
              ) : (
                <User size={24} />
              )}
            </div>
            <div>
              <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                {profileData.firstName} {profileData.lastName}
              </h2>
              <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                Premium Solver
              </p>
              <button
                onClick={() => {
                  if (uploadingPicture) return; // Prevent multiple uploads
                  // Create a file input element
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = 'image/*';
                  input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                      handleProfilePictureUpload(file);
                    }
                  };
                  input.click();
                }}
                disabled={uploadingPicture}
                className={`text-sm ${
                  uploadingPicture
                    ? 'text-gray-400 cursor-not-allowed'
                    : darkMode ? 'text-white/60 hover:text-white cursor-pointer' : 'text-[#00001a]/60 hover:text-[#00001a] cursor-pointer'
                } transition-colors`}
              >
                {uploadingPicture ? 'Uploading...' : 'Change Profile Picture'}
              </button>
            </div>
          </div>
        </div>

        {/* Profile Layout */}
        <div className="w-full">
          {/* Tab Navigation */}
          <div className="flex flex-wrap gap-3 mb-6">
              {tabs.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${
                    activeTab === tab
                      ? darkMode
                        ? 'bg-white text-[#00001a] font-semibold'
                        : 'bg-[#00001a] text-white font-semibold'
                      : darkMode
                        ? 'text-white bg-[#00001a] hover:bg-[#00001a]/80 font-normal'
                        : 'text-[#00001a] bg-white hover:bg-gray-50 font-normal'
                  }`}
                  style={{
                    boxShadow: activeTab === tab
                      ? darkMode
                        ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                        : '0 10px 25px rgba(0, 0, 26, 0.3), 0 6px 10px rgba(0, 0, 26, 0.15)'
                      : darkMode
                        ? '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)'
                        : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
                  }}
                  onMouseEnter={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = activeTab === tab
                        ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2)'
                        : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = activeTab === tab
                        ? '0 10px 25px rgba(255, 255, 255, 0.2), 0 6px 10px rgba(255, 255, 255, 0.1)'
                        : '0 4px 15px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(255, 255, 255, 0.05)';
                    }
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Total Sessions & Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Total Sessions */}
              <div className={`group p-4 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden cursor-pointer ${
                darkMode
                  ? 'rounded-lg bg-white/3 border-white/20 hover:bg-white/8 hover:border-white/30 hover-glow-dark'
                  : 'rounded-lg bg-white border-gray-200'
              }`}
              style={{
                boxShadow: 'none',
                transition: 'box-shadow 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (!darkMode) {
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (!darkMode) {
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}>
                <div className="flex items-center gap-2 mb-3">
                  <BarChart3 size={18} className={darkMode ? 'text-blue-400' : 'text-[#00001a]'} />
                  <h3 className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Total Sessions
                  </h3>
                </div>
                <div className="mb-2">
                  <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    127
                  </p>
                </div>
                <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  Sessions completed
                </p>
              </div>

              {/* Followers */}
              <div className={`group p-4 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden cursor-pointer ${
                darkMode
                  ? 'rounded-lg bg-white/3 border-white/20 hover:bg-white/8 hover:border-white/30 hover-glow-dark'
                  : 'rounded-lg bg-white border-gray-200'
              }`}
              style={{
                boxShadow: 'none',
                transition: 'box-shadow 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (!darkMode) {
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (!darkMode) {
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}>
                <div className="flex items-center gap-2 mb-3">
                  <Users size={18} className={darkMode ? 'text-green-400' : 'text-[#00001a]'} />
                  <h3 className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Followers
                  </h3>
                </div>
                <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  {followers.toLocaleString()}
                </div>
                <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  People following you
                </p>
              </div>

              {/* Rate Limit */}
              <div className={`group p-4 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden cursor-pointer ${
                darkMode
                  ? 'rounded-lg bg-white/3 border-white/20 hover:bg-white/8 hover:border-white/30 hover-glow-dark'
                  : 'rounded-lg bg-white border-gray-200'
              }`}
              style={{
                boxShadow: 'none',
                transition: 'box-shadow 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (!darkMode) {
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (!darkMode) {
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}>
                <div className="flex items-center gap-2 mb-3">
                  <Zap size={18} className={darkMode ? 'text-yellow-400' : 'text-[#00001a]'} />
                  <h3 className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Rate Limit
                  </h3>
                </div>
                <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  $45/hr
                </div>
                <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                  Current hourly rate
                </p>
              </div>
            </div>

            {/* Main Content */}
            <div className={`group p-6 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden ${
              darkMode
                ? 'rounded-lg bg-white/3 border-white/20 hover:bg-white/8 hover:border-white/30'
                : 'rounded-lg bg-white border-gray-200'
            }`}
            style={darkMode ? {
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
              transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
            } : {
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              if (darkMode) {
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2)';
              } else {
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
              }
            }}
            onMouseLeave={(e) => {
              if (darkMode) {
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
              } else {
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
              }
            }}>
              {/* Tab Content */}
              {activeTab === 'Profile' && (
                <div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
                  {/* Left Column - Personal Information */}
                  <div className={`p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden cursor-pointer ${
                    darkMode
                      ? 'bg-white/3 border-white/20 hover:bg-white/8 hover:border-white/30'
                      : 'bg-white border-gray-200 hover:border-gray-300'
                  }`}
                  style={darkMode ? {
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                    transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                  } : {
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 0 15px rgba(21, 58, 168, 0.2), 0 4px 12px rgba(0, 0, 0, 0.08)';
                      e.currentTarget.style.transform = 'translateY(-2px) scale(1.01)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                      e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    }
                  }}>
                    <div className="space-y-6">
                      <div>
                        <h3 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Personal Information
                        </h3>
                        <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Update your personal information and help seekers find the right solver
                        </p>
                      </div>

                    {/* Name Fields */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          First Name
                        </label>
                        <input
                          type="text"
                          value={profileData.firstName}
                          onChange={(e) => handleInputChange('firstName', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            darkMode
                              ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                              : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                          }`}
                          placeholder="Jane"
                        />
                      </div>
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Last Name
                        </label>
                        <input
                          type="text"
                          value={profileData.lastName}
                          onChange={(e) => handleInputChange('lastName', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            darkMode
                              ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                              : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                          }`}
                          placeholder="Doe"
                        />
                      </div>
                    </div>

                    {/* Email */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        Email
                      </label>
                      <input
                        type="email"
                        value={profileData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                            : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                        }`}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    {/* Phone Number */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={profileData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                            : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                        }`}
                        placeholder="+****************"
                      />
                    </div>

                    {/* Location Fields */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Country
                        </label>
                        <input
                          type="text"
                          value={profileData.country}
                          onChange={(e) => handleInputChange('country', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            darkMode
                              ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                              : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                          }`}
                          placeholder="United States"
                        />
                      </div>
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          City
                        </label>
                        <input
                          type="text"
                          value={profileData.city}
                          onChange={(e) => handleInputChange('city', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            darkMode
                              ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                              : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                          }`}
                          placeholder="San Francisco"
                        />
                      </div>
                    </div>

                    {/* Gender and Date of Birth */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Gender
                        </label>
                        <select
                          value={profileData.gender || ''}
                          onChange={(e) => handleInputChange('gender', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            darkMode
                              ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                              : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                          }`}
                        >
                          <option value="">Select Gender</option>
                          <option value="Female">Female</option>
                          <option value="Male">Male</option>
                          <option value="Other">Other</option>
                          <option value="Prefer not to say">Prefer not to say</option>
                        </select>
                      </div>
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Date of Birth
                        </label>
                        <input
                          type="date"
                          value={profileData.dateOfBirth || ''}
                          onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            darkMode
                              ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10'
                              : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                          }`}
                        />
                      </div>
                    </div>

                    {/* Professional Bio */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        Professional Bio
                      </label>
                      <textarea
                        value={profileData.bio || ''}
                        onChange={(e) => handleInputChange('bio', e.target.value)}
                        placeholder="Tell seekers about your expertise and experience"
                        rows={4}
                        className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 resize-none ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10 placeholder-white/50'
                            : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300 placeholder-gray-500'
                        }`}
                      />
                    </div>

                    </div>
                  </div>

                  {/* Right Column - Settings and Actions */}
                  <div className="space-y-6">
                    {/* Pseudoname Section */}
                    <div className={`p-6 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'bg-white/5 border-white/10 hover-glow-dark'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.08)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                      } else {
                        e.currentTarget.style.boxShadow = '';
                      }
                    }}>
                      <div className="mb-4">
                        <h4 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Your Pseudo Name
                        </h4>
                        <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                          Maintain your privacy while participating in discussions.
                        </p>
                        <div className={`flex items-center gap-2 text-xs mt-2 font-medium px-3 py-2 rounded ${darkMode ? 'text-yellow-400' : 'bg-[#00001a] text-white'}`}>
                          <AlertTriangle size={14} className={darkMode ? 'text-yellow-400' : 'text-white'} />
                          Once set, your pseudo name cannot be changed!
                        </div>
                      </div>

                      {isPseudonameSet ? (
                        <div className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                          darkMode ? 'bg-white/5 border-white/10 hover-glow-dark' : 'bg-white border-gray-200'
                        }`}
                        onMouseEnter={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1)';
                          } else {
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.08)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                          } else {
                            e.currentTarget.style.boxShadow = '';
                          }
                        }}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                                Your psudoname:
                              </p>
                              <p className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                {getDisplaypsudoname()}
                              </p>
                            </div>
                            <Lock size={20} className={darkMode ? 'text-white/40' : 'text-[#00001a]/40'} />
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {/* Choice Section */}
                          <div className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Choose how to create your pseudo name:
                          </div>

                          {/* Option Buttons */}
                          <div className="grid grid-cols-2 gap-3 mb-4">
                            <button
                              onClick={() => setUseCustomName(true)}
                              className={`px-4 py-3 rounded-lg font-medium transition-all duration-300 border ${
                                useCustomName
                                  ? darkMode
                                    ? 'bg-blue-500/30 text-blue-300 border-blue-500/50'
                                    : 'bg-[#00001a]/20 text-[#00001a] border-[#00001a]/30'
                                  : darkMode
                                    ? 'bg-white/5 text-white/70 border-white/20 hover:bg-white/10'
                                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
                              }`}
                            >
                              Create My Own
                            </button>
                            <button
                              onClick={() => setUseCustomName(false)}
                              className={`px-4 py-3 rounded-lg font-medium transition-all duration-300 border ${
                                !useCustomName
                                  ? darkMode
                                    ? 'bg-blue-500/30 text-blue-300 border-blue-500/50'
                                    : 'bg-[#00001a]/20 text-[#00001a] border-[#00001a]/30'
                                  : darkMode
                                    ? 'bg-white/5 text-white/70 border-white/20 hover:bg-white/10'
                                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
                              }`}
                            >
                              Auto Generate
                            </button>
                          </div>

                          {/* Custom Name Input */}
                          {useCustomName ? (
                            <div className="space-y-3">
                              <input
                                type="text"
                                value={pseudoname}
                                onChange={(e) => setPseudoname(e.target.value)}
                                onKeyPress={handlePseudonameKeyPress}
                                placeholder="Enter your pseudo name"
                                className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                                  darkMode
                                    ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10 placeholder-white/50'
                                    : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300 placeholder-gray-500'
                                }`}
                                maxLength={20}
                              />
                              <div className={`text-xs ${darkMode ? 'text-white/50' : 'text-gray-500'}`}>
                                Max 20 characters. Once set, it cannot be changed.
                              </div>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              <div className={`p-4 rounded-lg border ${
                                darkMode ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                              }`}>
                                <div className={`text-sm mb-2 ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                                  Generated Name:
                                </div>
                                <div className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                  {autoGeneratedpsudoname}
                                </div>
                              </div>
                              <div className={`text-xs ${darkMode ? 'text-white/50' : 'text-gray-500'}`}>
                                Click "Generate New" for a different name, or "Confirm" to use this one.
                              </div>
                              <button
                                onClick={() => {
                                  const adjectives = ['Swift', 'Clever', 'Bright', 'Sharp', 'Quick', 'Smart', 'Wise', 'Bold', 'Keen', 'Agile']
                                  const animals = ['Falcon', 'Eagle', 'Wolf', 'Fox', 'Tiger', 'Lion', 'Hawk', 'Raven', 'Lynx', 'Panther']
                                  const randomNum = Math.floor(Math.random() * 999) + 1
                                  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
                                  const animal = animals[Math.floor(Math.random() * animals.length)]
                                  const generatedName = `${adjective}${animal}${randomNum}`
                                  setAutoGeneratedpsudoname(generatedName)
                                }}
                                className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 border ${
                                  darkMode
                                    ? 'bg-white/5 text-white/70 border-white/20 hover:bg-white/10'
                                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
                                }`}
                              >
                                Generate New
                              </button>
                            </div>
                          )}

                          <button
                            onClick={savepsudoname}
                            disabled={useCustomName && !pseudoname.trim()}
                            className={`w-full px-4 py-3 rounded-lg font-medium transition-all duration-300 cursor-pointer ${
                              useCustomName && !pseudoname.trim()
                                ? darkMode
                                  ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30 cursor-not-allowed'
                                  : 'bg-gray-200 text-gray-400 border border-gray-300 cursor-not-allowed'
                                : darkMode
                                  ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border border-blue-500/30 hover-glow-dark'
                                : 'bg-[#00001a] text-white hover:bg-[#00001a]/90 border border-[#00001a]'
                            }`}
                          >
                            Set Pseudo Name
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Price Customization Section */}
                    <div className={`p-6 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'bg-white/5 border-white/10 hover-glow-dark'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.08)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                      } else {
                        e.currentTarget.style.boxShadow = '';
                      }
                    }}>
                      <h4 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Price Settings
                      </h4>

                      {/* Badge Status & Testing Toggle */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                            Badge Status:
                          </span>
                          <span className={`text-sm font-semibold ${hasPricingBadge ? (darkMode ? 'text-green-400' : 'text-green-600') : (darkMode ? 'text-red-400' : 'text-red-600')}`}>
                            {hasPricingBadge ? '✓ Earned' : '✗ Not Earned'}
                          </span>
                        </div>
                        {/* Testing Toggle - Remove in production */}
                        <button
                          onClick={togglePricingBadge}
                          className={`text-xs px-2 py-1 rounded ${darkMode ? 'bg-gray-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          {hasPricingBadge ? 'Remove Badge (Test)' : 'Grant Badge (Test)'}
                        </button>
                      </div>

                      {/* Current Price Display */}
                      <div className="mb-4">
                        <p className={`text-sm font-medium ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Fixed Price per Minute: <span className={`font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>{fixedPricePerMinute}/min</span>
                        </p>
                        {savedPrice && (
                          <p className={`text-sm font-medium mt-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            Current Rate: <span className="font-semibold">{savedPrice}/min</span>
                          </p>
                        )}
                      </div>

                      {/* Custom Price Input */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          Set Your Price ({minCustomPrice}-{maxCustomPrice}/min)
                        </label>

                        {!hasPricingBadge && (
                          <div className={`mb-3 p-3 rounded-lg border ${darkMode ? 'bg-yellow-500/10 border-yellow-500/30 text-yellow-400' : 'bg-[#00001a] border-[#00001a] text-white'}`}>
                            <div className="flex items-center gap-2 text-sm font-medium">
                              <Trophy size={16} className={darkMode ? 'text-yellow-400' : 'text-white'} />
                              You can set your own price after earning the required badge.
                            </div>
                          </div>
                        )}

                        <input
                          type="number"
                          value={customPrice}
                          onChange={(e) => handlePriceChange(e.target.value)}
                          placeholder={hasPricingBadge ? `Enter price (${minCustomPrice}-${maxCustomPrice})` : 'Badge required to set price'}
                          min={minCustomPrice}
                          max={maxCustomPrice}
                          step="0.1"
                          disabled={!hasPricingBadge}
                          className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                            !hasPricingBadge
                              ? darkMode
                                ? 'bg-gray-500/10 text-gray-400 border-gray-500/30 cursor-not-allowed'
                                : 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
                              : priceError
                                ? darkMode
                                  ? 'bg-red-500/10 text-white border-red-500/30 focus:border-red-500/50'
                                  : 'bg-red-50 text-[#00001a] border-red-300 focus:border-red-400'
                                : darkMode
                                  ? 'bg-white/5 text-white border-white/10 focus:border-white/30 focus:bg-white/10 placeholder-white/50'
                                  : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300 placeholder-gray-500'
                          }`}
                        />

                        {hasPricingBadge && !priceError && (
                          <p className={`text-xs mt-2 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            Set a price between {minCustomPrice} and {maxCustomPrice} per minute
                          </p>
                        )}

                        {priceError && (
                          <p className={`text-sm mt-2 ${darkMode ? 'text-red-400' : 'text-red-600'}`}>
                            {priceError}
                          </p>
                        )}

                        {/* Save Price Button */}
                        <button
                          onClick={saveCustomPrice}
                          disabled={!hasPricingBadge || !customPrice || !!priceError}
                          className={`mt-3 w-full px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
                            !hasPricingBadge || !customPrice || !!priceError
                              ? darkMode
                                ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30 cursor-not-allowed'
                                : 'bg-gray-200 text-gray-400 border border-gray-300 cursor-not-allowed'
                              : darkMode
                                ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border border-blue-500/30 hover-glow-dark'
                                : 'bg-[#00001a] text-white hover:bg-[#00001a]/90 border border-[#00001a]'
                          }`}
                          style={{
                            boxShadow: 'none',
                            transition: 'box-shadow 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            if (!hasPricingBadge || !customPrice || !!priceError) return
                            if (darkMode) {
                              e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.2)';
                            } else {
                              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 26, 0.3)';
                            }
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.boxShadow = 'none';
                          }}
                        >
                          {!hasPricingBadge ? 'Badge Required' : 'Save Price'}
                        </button>
                      </div>
                    </div>



                  </div>
                </div>

                <div className="flex items-center justify-end gap-4 mt-8">
                  {saveStatus && (
                    <div className={`flex items-center gap-2 text-sm ${
                      saveStatus === 'saving' ? (darkMode ? 'text-white/70' : 'text-[#00001a]/70') :
                      saveStatus === 'saved' ? (darkMode ? 'text-white' : 'text-[#00001a]') :
                      saveStatus === 'error' ? (darkMode ? 'text-white' : 'text-[#00001a]') : ''
                    }`}>
                      {saveStatus === 'saving' && (
                        <>
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                          Saving...
                        </>
                      )}
                      {saveStatus === 'saved' && (
                        <>
                          <span>✓</span>
                          Changes saved successfully!
                        </>
                      )}
                      {saveStatus === 'error' && (
                        <>
                          <span>✗</span>
                          Failed to save changes
                        </>
                      )}
                    </div>
                  )}
                  <button
                    onClick={handleSaveProfile}
                    disabled={saveStatus === 'saving'}
                    className={`px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 backdrop-blur-sm border ${
                      saveStatus === 'saving'
                        ? 'cursor-not-allowed opacity-50'
                        : 'cursor-pointer hover:scale-105'
                    } ${
                      saveStatus === 'saving'
                        ? darkMode
                          ? 'bg-white/10 text-white/50 border-white/20'
                          : 'bg-[#00001a]/50 text-white/50 border-[#00001a]/10'
                        : darkMode
                          ? 'bg-white text-[#00001a] border-white/30 hover:bg-white/90'
                          : 'bg-[#00001a] text-white border-[#00001a]/20 hover:bg-[#00001a]/90'
                    }`}
                    onMouseEnter={(e) => {
                      if (saveStatus !== 'saving') {
                        if (darkMode) {
                          e.currentTarget.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1)';
                        } else {
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.08)';
                        }
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (saveStatus !== 'saving') {
                        e.currentTarget.style.boxShadow = '';
                      }
                    }}
                  >
                    {saveStatus === 'saving' ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
                </div>
              )}

              {/* Expertise Tab */}
              {activeTab === 'Expertise' && (
                <div>
                  {/* Hide reference image buttons for this tab */}
                  <style>
                    {`
                      .reference-image-button,
                      [class*="reference-image"],
                      [class*="reference-btn"],
                      [data-reference="image"],
                      button[title*="reference" i],
                      button[title*="image" i],
                      .image-reference-btn {
                        display: none !important;
                      }
                    `}
                  </style>
                  <h3 className={`text-xl font-semibold mb-6 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Skills & Expertise
                  </h3>

                  {/* Skills Section */}
                  <div className="mb-8">
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Technical Skills
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {skillCategories.map((category) => (
                        <div key={category} className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                          darkMode
                            ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                            : 'bg-white border-gray-200 shadow-md hover:shadow-xl'
                        }`}>
                          <h5 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {category}
                          </h5>
                          <div className="flex items-center gap-2">
                            <div className={`flex-1 h-2 rounded-full ${darkMode ? 'bg-white/10' : 'bg-gray-200'}`}>
                              <div className={`h-2 rounded-full ${darkMode ? 'bg-white' : 'bg-[#00001a]'}`}
                                   style={{width: `${Math.floor(Math.random() * 40) + 60}%`}}></div>
                            </div>
                            <span className={`text-xs ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              Expert
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Languages */}
                  <div className="mb-8">
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Languages
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {languages.slice(0, 5).map((language) => (
                        <span key={language} className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
                          darkMode
                            ? 'bg-white/10 text-white shadow-md hover:shadow-lg hover-glow-dark'
                            : 'bg-gray-100 text-[#00001a] shadow-sm hover:shadow-md'
                        }`}>
                          {language}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Projects Overview */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Recent Projects
                      </h4>
                      <button
                        onClick={handleAddProject}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 backdrop-blur-sm border cursor-pointer ${
                          darkMode
                            ? 'bg-[#00001a] text-white border-white/20 hover:bg-[#00001a]/90 hover-glow-dark'
                            : 'bg-[#00001a] text-white border-[#00001a] hover:bg-[#00001a]/90'
                        }`}
                      >
                        <Plus size={16} />
                        Add Project
                      </button>
                    </div>

                    {projects.length === 0 ? (
                      <div className={`text-center py-12 rounded-lg border-2 border-dashed transition-all duration-300 cursor-pointer ${
                        darkMode
                          ? 'border-white/20 text-white/60 shadow-lg hover:shadow-xl hover-glow-dark'
                          : 'border-gray-300 text-gray-500 shadow-md hover:shadow-lg'
                      }`}>
                        <Plus size={48} className="mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">No projects yet</p>
                        <p className="text-sm mb-4">Add your first project to showcase your work</p>
                        <button
                          onClick={handleAddProject}
                          className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
                            darkMode
                              ? 'bg-white/10 text-white hover:bg-white/20 hover-glow-dark'
                              : 'bg-gray-100 text-[#00001a] hover:bg-gray-200'
                          }`}
                        >
                          Add Your First Project
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {projects.map((project) => (
                          <div key={project.id} className={`p-6 rounded-lg border transition-all duration-300 cursor-pointer ${
                            darkMode
                              ? 'bg-white/5 border-white/10 hover:bg-white/8 shadow-lg hover:shadow-2xl hover-glow-dark'
                              : 'bg-white border-gray-200 hover:bg-gray-50 shadow-md hover:shadow-xl'
                          }`}>
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h5 className={`font-semibold text-lg ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                    {project.title}
                                  </h5>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    project.status === 'Completed'
                                      ? darkMode ? 'bg-green-500/20 text-green-400' : 'bg-[#00001a]/10 text-[#00001a]'
                                      : darkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-[#00001a]/10 text-[#00001a]'
                                  }`}>
                                    {project.status}
                                  </span>
                                </div>
                                {project.duration && (
                                  <p className={`text-sm mb-2 ${darkMode ? 'text-white/60' : 'text-gray-600'}`}>
                                    Duration: {project.duration}
                                  </p>
                                )}
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  onClick={() => handleEditProject(project)}
                                  className={`p-2 rounded-lg transition-all duration-300 ${
                                    darkMode
                                      ? 'text-white/70 hover:bg-white/10 hover:text-white'
                                      : 'text-gray-600 hover:bg-gray-100 hover:text-[#00001a]'
                                  }`}
                                  title="Edit project"
                                >
                                  <Edit3 size={16} />
                                </button>
                                <button
                                  onClick={() => handleDeleteProject(project.id)}
                                  className={`p-2 rounded-lg transition-all duration-300 ${
                                    darkMode
                                      ? 'text-red-400 hover:bg-red-500/20'
                                      : 'text-red-600 hover:bg-red-100'
                                  }`}
                                  title="Delete project"
                                >
                                  <Trash2 size={16} />
                                </button>
                              </div>
                            </div>

                            <p className={`text-sm mb-4 leading-relaxed ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              {project.description}
                            </p>

                            {project.link && (
                              <div className="mb-4">
                                <a
                                  href={project.link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className={`text-sm font-medium transition-all duration-300 ${
                                    darkMode
                                      ? 'text-blue-400 hover:text-blue-300'
                                      : 'text-[#00001a] hover:text-[#00001a]/80'
                                  }`}
                                >
                                  View Project →
                                </a>
                              </div>
                            )}

                            <div className="flex flex-wrap gap-2">
                              {project.technologies.map((tech) => (
                                <span key={tech} className={`px-3 py-1 rounded-full text-xs font-medium ${
                                  darkMode ? 'bg-white/10 text-white/80' : 'bg-gray-100 text-[#00001a]/80'
                                }`}>
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Availability Tab */}
              {activeTab === 'Availability' && (
                <div>
                  {/* Hide reference image buttons for this tab and ensure dropdown visibility */}
                  <style>
                    {`
                      .reference-image-button,
                      [class*="reference-image"],
                      [class*="reference-btn"],
                      [data-reference="image"],
                      button[title*="reference" i],
                      button[title*="image" i],
                      .image-reference-btn {
                        display: none !important;
                      }

                      /* Ensure dropdowns are visible and properly positioned */
                      .availability-section {
                        position: relative;
                        overflow: visible !important;
                      }

                      .availability-day-row {
                        position: relative;
                        overflow: visible !important;
                      }

                      /* Ensure time picker dropdowns appear above other elements */
                      .time-picker-dropdown {
                        z-index: 9999 !important;
                        position: absolute !important;
                      }

                      /* Prevent parent containers from clipping dropdowns */
                      .availability-container {
                        overflow: visible !important;
                      }

                      /* Ensure proper stacking context */
                      .availability-section > div {
                        position: relative;
                        z-index: 1;
                      }

                      .availability-section .space-y-4 {
                        overflow: visible !important;
                      }
                    `}
                  </style>
                  <h3 className={`text-xl font-semibold mb-6 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Availability Settings
                  </h3>
                  <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Manage your availability and scheduling preferences
                  </p>

                  <div className="availability-container" style={{ overflow: 'visible', position: 'relative' }}>

                  {/* Session Availability */}
                  <div className={`availability-section p-6 rounded-lg border mb-6 transition-all duration-300 cursor-pointer ${
                    darkMode
                      ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                      : 'bg-white border-gray-200 shadow-md hover:shadow-xl'
                  }`} style={{ overflow: 'visible' }}>
                    <h4 className={`text-lg font-medium mb-4 flex items-center gap-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      <Calendar className="w-5 h-5" />
                      Session Availability
                    </h4>
                    <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Set your available hours for regular tutoring sessions
                    </p>

                    <div className="space-y-4" style={{ overflow: 'visible' }}>
                      {daysOfWeek.map((day) => (
                        <div key={day} className={`availability-day-row flex items-center justify-between p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                          darkMode
                            ? 'bg-white/3 border-white/10 hover:bg-white/5 shadow-md hover:shadow-lg hover-glow-dark'
                            : 'bg-white border-gray-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                        }`} style={{ overflow: 'visible', position: 'relative' }}>
                          <div className="flex items-center gap-4">
                            <button
                              onClick={() => handleScheduleToggle(day)}
                              className={`w-12 h-6 rounded-full transition-all duration-300 relative shadow-inner ${
                                weeklySchedule[day].enabled
                                  ? darkMode
                                    ? 'bg-white shadow-white/20'
                                    : 'bg-[#00001a] shadow-[#00001a]/30'
                                  : darkMode
                                    ? 'bg-white/20 shadow-black/20'
                                    : 'bg-gray-300 shadow-gray-400/30'
                              }`}
                            >
                              <div className={`w-5 h-5 rounded-full transition-all duration-300 absolute top-0.5 shadow-md ${
                                weeklySchedule[day].enabled
                                  ? darkMode
                                    ? 'right-0.5 bg-[#00001a] shadow-black/30'
                                    : 'right-0.5 bg-white shadow-gray-300/50'
                                  : darkMode
                                    ? 'left-0.5 bg-white shadow-white/30'
                                    : 'left-0.5 bg-white shadow-gray-400/50'
                              }`}></div>
                            </button>
                            <span className={`text-sm font-medium capitalize min-w-[80px] ${
                              darkMode ? 'text-white' : 'text-[#00001a]'
                            }`}>
                              {day}
                            </span>
                          </div>

                          {weeklySchedule[day].enabled && (
                            <div className="flex items-center gap-3">
                              <CustomTimePicker
                                value={weeklySchedule[day].startTime}
                                onChange={(value) => handleTimeChange(day, 'startTime', value)}
                                darkMode={darkMode}
                                className={`px-3 py-2 rounded-lg text-sm backdrop-blur-sm border transition-all duration-300 ${
                                  darkMode
                                    ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                                    : 'bg-white text-[#00001a] border-gray-200 focus:border-[#00001a]'
                                }`}
                              />
                              <span className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                                to
                              </span>
                              <CustomTimePicker
                                value={weeklySchedule[day].endTime}
                                onChange={(value) => handleTimeChange(day, 'endTime', value)}
                                darkMode={darkMode}
                                className={`px-3 py-2 rounded-lg text-sm backdrop-blur-sm border transition-all duration-300 ${
                                  darkMode
                                    ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                                    : 'bg-white text-[#00001a] border-gray-200 focus:border-[#00001a]'
                                }`}
                              />
                            </div>
                          )}

                          {!weeklySchedule[day].enabled && (
                            <span className={`text-sm ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                              Unavailable
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Podcast Availability */}
                  <div className={`availability-section p-6 rounded-lg border mb-6 transition-all duration-300 cursor-pointer ${
                    darkMode
                      ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                      : 'bg-white border-gray-200 shadow-md hover:shadow-xl'
                  }`} style={{ overflow: 'visible' }}>
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      🎙️ Podcast Availability
                    </h4>
                    <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Set your available hours for podcast recording sessions
                    </p>

                    <div className="space-y-4" style={{ overflow: 'visible' }}>
                      {daysOfWeek.map((day) => (
                        <div key={day} className={`availability-day-row flex items-center justify-between p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                          darkMode
                            ? 'bg-white/3 border-white/10 hover:bg-white/5 shadow-md hover:shadow-lg hover-glow-dark'
                            : 'bg-white border-gray-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                        }`} style={{ overflow: 'visible', position: 'relative' }}>
                          <div className="flex items-center gap-4">
                            <button
                              onClick={() => handlePodcastScheduleToggle(day)}
                              className={`w-12 h-6 rounded-full transition-all duration-300 relative shadow-inner ${
                                podcastSchedule[day].enabled
                                  ? darkMode
                                    ? 'bg-white shadow-white/20'
                                    : 'bg-[#00001a] shadow-[#00001a]/30'
                                  : darkMode
                                    ? 'bg-white/20 shadow-black/20'
                                    : 'bg-gray-300 shadow-gray-400/30'
                              }`}
                            >
                              <div className={`w-5 h-5 rounded-full transition-all duration-300 absolute top-0.5 shadow-md ${
                                podcastSchedule[day].enabled
                                  ? darkMode
                                    ? 'right-0.5 bg-[#00001a] shadow-black/30'
                                    : 'right-0.5 bg-white shadow-gray-300/50'
                                  : darkMode
                                    ? 'left-0.5 bg-white shadow-white/30'
                                    : 'left-0.5 bg-white shadow-gray-400/50'
                              }`}></div>
                            </button>
                            <span className={`text-sm font-medium capitalize min-w-[80px] ${
                              darkMode ? 'text-white' : 'text-[#00001a]'
                            }`}>
                              {day}
                            </span>
                          </div>

                          {podcastSchedule[day].enabled && (
                            <div className="flex items-center gap-3">
                              <CustomTimePicker
                                value={podcastSchedule[day].startTime}
                                onChange={(value) => handlePodcastTimeChange(day, 'startTime', value)}
                                darkMode={darkMode}
                                className={`px-3 py-2 rounded-lg text-sm backdrop-blur-sm border transition-all duration-300 ${
                                  darkMode
                                    ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                                    : 'bg-white text-[#00001a] border-gray-200 focus:border-[#00001a]'
                                }`}
                              />
                              <span className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                                to
                              </span>
                              <CustomTimePicker
                                value={podcastSchedule[day].endTime}
                                onChange={(value) => handlePodcastTimeChange(day, 'endTime', value)}
                                darkMode={darkMode}
                                className={`px-3 py-2 rounded-lg text-sm backdrop-blur-sm border transition-all duration-300 ${
                                  darkMode
                                    ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                                    : 'bg-white text-[#00001a] border-gray-200 focus:border-[#00001a]'
                                }`}
                              />
                            </div>
                          )}

                          {!podcastSchedule[day].enabled && (
                            <span className={`text-sm ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                              Unavailable
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Time Zone */}
                  <div className={`p-6 rounded-lg border mb-6 transition-all duration-300 cursor-pointer ${
                    darkMode
                      ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                      : 'bg-white border-gray-200 shadow-md hover:shadow-xl'
                  }`}>
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Time Zone
                    </h4>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`text-sm mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Current: {timeZones.find(tz => tz.value === selectedTimeZone)?.label || selectedTimeZone}
                        </p>
                        <p className={`text-xs ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                          This affects how your availability is displayed to students
                        </p>
                      </div>
                      <button
                        onClick={() => setShowTimeZoneModal(true)}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 backdrop-blur-sm border cursor-pointer ${
                          darkMode
                            ? 'bg-white/10 text-white border-white/20 hover:bg-white/20 hover:shadow-lg hover-glow-dark'
                            : 'bg-gray-100 text-[#00001a] border-gray-200 hover:bg-gray-200'
                        }`}
                      >
                        Change
                      </button>
                    </div>
                  </div>

                  </div> {/* Close availability-container */}

                  {/* Save Button */}
                  <div className="flex items-center justify-end gap-4">
                    {availabilitySaveStatus && (
                      <div className={`flex items-center gap-2 text-sm ${
                        availabilitySaveStatus === 'saving' ? (darkMode ? 'text-white/70' : 'text-[#00001a]/70') :
                        availabilitySaveStatus === 'saved' ? (darkMode ? 'text-white' : 'text-[#00001a]') :
                        availabilitySaveStatus === 'error' ? (darkMode ? 'text-white' : 'text-[#00001a]') : ''
                      }`}>
                        {availabilitySaveStatus === 'saving' && (
                          <>
                            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                            Saving availability...
                          </>
                        )}
                        {availabilitySaveStatus === 'saved' && (
                          <>
                            <span>✓</span>
                            Availability saved successfully!
                          </>
                        )}
                        {availabilitySaveStatus === 'error' && (
                          <>
                            <span>✗</span>
                            Failed to save availability
                          </>
                        )}
                      </div>
                    )}
                    <button
                      onClick={handleSaveAvailability}
                      disabled={availabilitySaveStatus === 'saving'}
                      className={`px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 backdrop-blur-sm border cursor-pointer ${
                        availabilitySaveStatus === 'saving'
                          ? darkMode
                            ? 'bg-white/10 text-white/50 border-white/20 cursor-not-allowed'
                            : 'bg-[#00001a]/50 text-white/70 border-[#00001a]/20 cursor-not-allowed'
                          : darkMode
                            ? 'bg-white text-[#00001a] border-white/30 hover:bg-white/90 hover-glow-dark'
                            : 'bg-[#00001a] text-white border-[#00001a]/20 hover:bg-[#00001a]/90'
                      }`}

                    >
                      {availabilitySaveStatus === 'saving' ? 'Saving...' : 'Save Availability'}
                    </button>
                  </div>
                </div>
              )}

              {/* Payment Tab */}
              {activeTab === 'Payment' && (
                <div>
                  {/* Hide reference image buttons for this tab */}
                  <style>
                    {`
                      .reference-image-button,
                      [class*="reference-image"],
                      [class*="reference-btn"],
                      [data-reference="image"],
                      button[title*="reference" i],
                      button[title*="image" i],
                      .image-reference-btn {
                        display: none !important;
                      }
                    `}
                  </style>
                  <h3 className={`text-xl font-semibold mb-6 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Payment & Earnings
                  </h3>
                  <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Manage your payment methods, view earnings, and configure withdrawal settings
                  </p>

                  {/* Payment Overview Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    {/* Total Earnings */}
                    <div className={`p-6 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                        : 'bg-white border-[#00001a]/20 shadow-md hover:shadow-xl'
                    }`}
                    style={{
                      boxShadow: 'none',
                      transition: 'box-shadow 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!darkMode) {
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!darkMode) {
                        e.currentTarget.style.boxShadow = 'none';
                      }
                    }}>
                      <div className="flex items-center justify-between mb-4">
                        <h4 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Total Earnings
                        </h4>
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          darkMode ? 'bg-green-500/20 text-green-400' : 'bg-[#00001a]/10 text-[#00001a]'
                        }`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 6v6l4 2"/>
                          </svg>
                        </div>
                      </div>
                      <p className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        $2,450.00
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-green-400' : 'text-[#00001a]/70'}`}>
                        +12% from last month
                      </p>
                    </div>

                    {/* Available Balance */}
                    <div className={`p-6 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                        : 'bg-white border-[#00001a]/20 shadow-md hover:shadow-xl'
                    }`}
                    style={{
                      boxShadow: 'none',
                      transition: 'box-shadow 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!darkMode) {
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!darkMode) {
                        e.currentTarget.style.boxShadow = 'none';
                      }
                    }}>
                      <div className="flex items-center justify-between mb-4">
                        <h4 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Available Balance
                        </h4>
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          darkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-[#00001a]/10 text-[#00001a]'
                        }`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                            <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                            <line x1="1" y1="10" x2="23" y2="10"/>
                          </svg>
                        </div>
                      </div>
                      <p className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        $850.00
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        Ready for withdrawal
                      </p>
                    </div>

                    {/* Pending Payments */}
                    <div className={`p-6 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                        : 'bg-white border-[#00001a]/20 shadow-md hover:shadow-xl'
                    }`}
                    style={{
                      boxShadow: 'none',
                      transition: 'box-shadow 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!darkMode) {
                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 26, 0.15), 0 4px 12px rgba(0, 0, 26, 0.1)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!darkMode) {
                        e.currentTarget.style.boxShadow = 'none';
                      }
                    }}>
                      <div className="flex items-center justify-between mb-4">
                        <h4 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Pending Payments
                        </h4>
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          darkMode ? 'bg-yellow-500/20 text-yellow-400' : 'bg-[#00001a]/10 text-[#00001a]'
                        }`}>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                          </svg>
                        </div>
                      </div>
                      <p className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        $320.00
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-yellow-400' : 'text-[#00001a]/70'}`}>
                        3 sessions pending
                      </p>
                    </div>
                  </div>



                  {/* Recent Transactions & Withdrawal Settings */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Recent Transactions */}
                    <div id="recent-transactions-container" className={`p-6 rounded-xl border transition-all duration-300 cursor-pointer relative overflow-hidden ${
                      darkMode
                        ? 'bg-white/3 border-white/20 shadow-[0_0_20px_rgba(59,130,246,0.15)] hover:shadow-[0_0_25px_rgba(59,130,246,0.25)] hover:border-blue-400/40'
                        : 'bg-white border-[#00001a]/30 shadow-md hover:shadow-xl'
                    }`}
                    style={darkMode ? {
                      boxShadow: '0_0_20px_rgba(59,130,246,0.15), inset_0_1px_0_rgba(255,255,255,0.1)',
                      backdropFilter: 'blur(10px)'
                    } : {}}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0_0_25px_rgba(59,130,246,0.25), inset_0_1px_0_rgba(255,255,255,0.15)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.15), inset_0_1px_0_rgba(255,255,255,0.1)';
                      }
                    }}>
                      <div className="flex items-center justify-between mb-6">
                        <h4 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Recent Transactions
                        </h4>
                        <button
                          onClick={handleViewTransactions}
                          className={`text-sm font-medium transition-all duration-300 ${
                            darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-[#00001a] hover:text-[#00001a]/80'
                          }`}
                        >
                          View All
                        </button>
                      </div>
                      <div className="space-y-4">
                        {transactions.slice(0, 4).map((transaction) => (
                          <div key={transaction.id} className={`flex items-center justify-between p-4 rounded-lg border transition-all duration-300 cursor-pointer relative overflow-hidden ${
                            darkMode
                              ? 'bg-white/2 border-white/10 shadow-[0_0_15px_rgba(59,130,246,0.1)] hover:shadow-[0_0_20px_rgba(59,130,246,0.2)] hover:border-blue-400/30'
                              : 'bg-white border-[#00001a]/20'
                          }`}
                          style={darkMode ? {
                            boxShadow: '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)',
                            backdropFilter: 'blur(5px)'
                          } : {}}
                          onMouseEnter={(e) => {
                            if (darkMode) {
                              e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.2), inset_0_1px_0_rgba(255,255,255,0.1)';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (darkMode) {
                              e.currentTarget.style.boxShadow = '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)';
                            }
                          }}>
                            <div className="flex items-center gap-3">
                              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                darkMode ? 'bg-white/10 text-white' : 'bg-[#00001a]/10 text-[#00001a]'
                              }`}>
                                {transaction.type === 'Earning' ? (
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                                    <line x1="12" y1="19" x2="12" y2="5"/>
                                    <polyline points="5,12 12,5 19,12"/>
                                  </svg>
                                ) : (
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                                    <line x1="12" y1="5" x2="12" y2="19"/>
                                    <polyline points="19,12 12,19 5,12"/>
                                  </svg>
                                )}
                              </div>
                              <div>
                                <p className={`font-medium text-sm ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                  {transaction.description}
                                </p>
                                <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                                  {transaction.date}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className={`font-medium text-sm ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                                {transaction.type === 'Earning' ? '+' : '-'}${transaction.amount}
                              </p>
                              <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                                {transaction.status}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Withdrawal Settings */}
                    <div id="withdrawal-settings-container" className={`p-6 rounded-xl border transition-all duration-300 cursor-pointer relative overflow-hidden ${
                      darkMode
                        ? 'bg-white/3 border-white/20 shadow-[0_0_20px_rgba(59,130,246,0.15)] hover:shadow-[0_0_25px_rgba(59,130,246,0.25)] hover:border-blue-400/40'
                        : 'bg-white border-[#00001a]/30 shadow-md hover:shadow-xl'
                    }`}
                    style={darkMode ? {
                      boxShadow: '0_0_20px_rgba(59,130,246,0.15), inset_0_1px_0_rgba(255,255,255,0.1)',
                      backdropFilter: 'blur(10px)'
                    } : {}}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0_0_25px_rgba(59,130,246,0.25), inset_0_1px_0_rgba(255,255,255,0.15)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.15), inset_0_1px_0_rgba(255,255,255,0.1)';
                      }
                    }}>
                      <div className="flex items-center justify-between mb-6">
                        <h4 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                          Withdrawal Settings
                        </h4>
                        <button
                          onClick={handlePaymentPreferences}
                          className={`text-sm font-medium transition-all duration-300 ${
                            darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-[#00001a] hover:text-[#00001a]/80'
                          }`}
                        >
                          Configure
                        </button>
                      </div>
                      <div className="space-y-4">
                        <div className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer relative overflow-hidden ${
                          darkMode
                            ? 'bg-white/2 border-white/10 shadow-[0_0_15px_rgba(59,130,246,0.1)] hover:shadow-[0_0_20px_rgba(59,130,246,0.2)] hover:border-blue-400/30'
                            : 'bg-white border-[#00001a]/20'
                        }`}
                        style={darkMode ? {
                          boxShadow: '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)',
                          backdropFilter: 'blur(5px)'
                        } : {}}
                        onMouseEnter={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.2), inset_0_1px_0_rgba(255,255,255,0.1)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)';
                          }
                        }}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              Auto Withdrawal
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              paymentPreferences.autoWithdraw
                                ? darkMode ? 'bg-green-500/20 text-green-400' : 'bg-[#00001a]/10 text-[#00001a]'
                                : darkMode ? 'bg-red-500/20 text-red-400' : 'bg-[#00001a]/10 text-[#00001a]'
                            }`}>
                              {paymentPreferences.autoWithdraw ? 'Enabled' : 'Disabled'}
                            </span>
                          </div>
                          <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            Automatically withdraw when threshold is reached
                          </p>
                        </div>
                        <div className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer relative overflow-hidden ${
                          darkMode
                            ? 'bg-white/2 border-white/10 shadow-[0_0_15px_rgba(59,130,246,0.1)] hover:shadow-[0_0_20px_rgba(59,130,246,0.2)] hover:border-blue-400/30'
                            : 'bg-white border-[#00001a]/20'
                        }`}
                        style={darkMode ? {
                          boxShadow: '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)',
                          backdropFilter: 'blur(5px)'
                        } : {}}
                        onMouseEnter={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.2), inset_0_1px_0_rgba(255,255,255,0.1)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)';
                          }
                        }}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              Withdrawal Threshold
                            </span>
                            <span className={`text-sm font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              ${paymentPreferences.withdrawalThreshold}
                            </span>
                          </div>
                          <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            Minimum amount before auto withdrawal
                          </p>
                        </div>
                        <div className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer relative overflow-hidden ${
                          darkMode
                            ? 'bg-white/2 border-white/10 shadow-[0_0_15px_rgba(59,130,246,0.1)] hover:shadow-[0_0_20px_rgba(59,130,246,0.2)] hover:border-blue-400/30'
                            : 'bg-white border-[#00001a]/20'
                        }`}
                        style={darkMode ? {
                          boxShadow: '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)',
                          backdropFilter: 'blur(5px)'
                        } : {}}
                        onMouseEnter={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.2), inset_0_1px_0_rgba(255,255,255,0.1)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (darkMode) {
                            e.currentTarget.style.boxShadow = '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)';
                          }
                        }}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              Frequency
                            </span>
                            <span className={`text-sm font-bold capitalize ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                              {paymentPreferences.frequency}
                            </span>
                          </div>
                          <p className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            How often to process withdrawals
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === 'Settings' && (
                <div>
                  {/* Hide reference image buttons for this tab */}
                  <style>
                    {`
                      .reference-image-button,
                      [class*="reference-image"],
                      [class*="reference-btn"],
                      [data-reference="image"],
                      button[title*="reference" i],
                      button[title*="image" i],
                      .image-reference-btn {
                        display: none !important;
                      }
                    `}
                  </style>
                  <h3 className={`text-xl font-semibold mb-6 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Account Settings
                  </h3>

                  {/* Security Settings */}
                  <div className="mb-8">
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Security
                    </h4>
                    <div className="space-y-4">
                      <button
                        onClick={handleChangePassword}
                        className={`w-full p-4 rounded-lg border text-left transition-all duration-300 backdrop-blur-sm cursor-pointer ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 hover:bg-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                            : 'bg-white border-gray-200 hover:bg-gray-50 text-[#00001a] shadow-md hover:shadow-xl'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium">Change Password</h5>
                            <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              Update your account password
                            </p>
                          </div>
                          <Lock size={18} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
                        </div>
                      </button>

                      <button
                        onClick={handleTwoFactorAuth}
                        className={`w-full p-4 rounded-lg border text-left transition-all duration-300 backdrop-blur-sm cursor-pointer ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 hover:bg-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                            : 'bg-white border-gray-200 hover:bg-gray-50 text-[#00001a] shadow-md hover:shadow-xl'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium">Two-Factor Authentication</h5>
                            <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              Add an extra layer of security
                            </p>
                          </div>
                          <Smartphone size={18} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
                        </div>
                      </button>
                    </div>
                  </div>

                  {/* Bank Account */}
                  <div className="mb-8">
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Payment Information
                    </h4>
                    <div className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                      darkMode
                        ? 'bg-white/5 border-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                        : 'bg-white border-gray-200 shadow-md hover:shadow-xl'
                    }`}>
                      <h5 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        Bank Account
                      </h5>
                      <p className={`text-sm mb-4 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        Add your bank account for payments
                      </p>
                      <button
                        onClick={handleAddBankAccount}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 backdrop-blur-sm border cursor-pointer ${
                          darkMode
                            ? 'bg-white/10 text-white border-white/20 hover:bg-white/20 hover:shadow-lg hover-glow-dark'
                            : 'bg-gray-100 text-[#00001a] border-gray-200 hover:bg-gray-200'
                        }`}
                      >
                        Add Bank Account
                      </button>
                    </div>
                  </div>

                  {/* Danger Zone */}
                  <div className="mb-8">
                    <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Danger Zone
                    </h4>
                    <div className="space-y-3">
                      <button
                        onClick={handleDeactivateAccount}
                        className={`w-full p-4 rounded-lg border text-left transition-all duration-300 backdrop-blur-sm cursor-pointer ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 hover:bg-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                            : 'bg-white border-gray-200 hover:bg-gray-50 text-[#00001a] shadow-md hover:shadow-xl'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium">Deactivate Account</h5>
                            <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              Temporarily disable your account
                            </p>
                          </div>
                          <Pause size={18} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
                        </div>
                      </button>

                      <button
                        onClick={handleDeleteAccount}
                        className={`w-full p-4 rounded-lg border text-left transition-all duration-300 backdrop-blur-sm cursor-pointer ${
                          darkMode
                            ? 'bg-white/5 text-white border-white/10 hover:bg-white/10 shadow-lg hover:shadow-2xl hover-glow-dark'
                            : 'bg-white border-gray-200 hover:bg-gray-50 text-[#00001a] shadow-md hover:shadow-xl'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium">Delete Account</h5>
                            <p className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                              Permanently delete your account and data
                            </p>
                          </div>
                          <Trash2 size={18} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modals */}
      {/* Deactivate Account Confirmation */}
      {showDeactivateConfirm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full p-6 rounded-lg backdrop-blur-xl border ${
            darkMode ? 'bg-[#00001a]/90 border-white/20' : 'bg-white/90 border-gray-200'
          }`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Deactivate Account
            </h3>
            <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Are you sure you want to deactivate your account? You can reactivate it later by logging in.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowDeactivateConfirm(false)}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
                  darkMode ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20 hover-glow-dark' : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeactivateAccount}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
                  darkMode ? 'bg-white text-[#00001a] hover:bg-white/90 hover-glow-dark' : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                }`}
              >
                Deactivate
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Account Confirmation */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full p-6 rounded-lg backdrop-blur-xl border ${
            darkMode ? 'bg-[#00001a]/90 border-white/20' : 'bg-white/90 border-gray-200'
          }`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Delete Account
            </h3>
            <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Are you sure you want to permanently delete your account? This action cannot be undone and all your data will be lost.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
                  darkMode ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20 hover-glow-dark' : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteAccount}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer ${
                  darkMode ? 'bg-white text-[#00001a] hover:bg-white/90 hover-glow-dark' : 'bg-red-100 text-red-700 border border-red-200 hover:bg-red-200'
                }`}
              >
                Delete Forever
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Password Change Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full p-6 rounded-lg backdrop-blur-xl border ${
            darkMode ? 'bg-[#00001a]/90 border-white/20' : 'bg-white/90 border-gray-200'
          }`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Change Password
            </h3>
            <div className="space-y-4 mb-6">
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Current Password
                </label>
                <input
                  type="password"
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode ? 'bg-white/5 text-white border-white/10 focus:border-white/30' : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter current password"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  New Password
                </label>
                <input
                  type="password"
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode ? 'bg-white/5 text-white border-white/10 focus:border-white/30' : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter new password"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Confirm New Password
                </label>
                <input
                  type="password"
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode ? 'bg-white/5 text-white border-white/10 focus:border-white/30' : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Confirm new password"
                />
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowPasswordModal(false)}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20' : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('🔒 Password changed successfully')
                  setShowPasswordModal(false)
                  alert('Password changed successfully!')
                }}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white text-[#00001a] hover:bg-white/90' : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                }`}
              >
                Change Password
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Two-Factor Authentication Modal */}
      {show2FAModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full p-6 rounded-lg backdrop-blur-xl border ${
            darkMode ? 'bg-[#00001a]/90 border-white/20' : 'bg-white/90 border-gray-200'
          }`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Two-Factor Authentication
            </h3>
            <p className={`text-sm mb-6 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Scan this QR code with your authenticator app to set up two-factor authentication.
            </p>
            <div className={`w-48 h-48 mx-auto mb-6 rounded-lg border-2 border-dashed flex items-center justify-center ${
              darkMode ? 'border-white/20 bg-white/5' : 'border-gray-300 bg-gray-100'
            }`}>
              <span className={`text-sm ${darkMode ? 'text-white/50' : 'text-gray-500'}`}>QR Code Here</span>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShow2FAModal(false)}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20' : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('📱 2FA setup completed')
                  setShow2FAModal(false)
                  alert('Two-factor authentication enabled successfully!')
                }}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white text-[#00001a] hover:bg-white/90' : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                }`}
              >
                Enable 2FA
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bank Account Modal */}
      {showBankModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full p-6 rounded-lg backdrop-blur-xl border ${
            darkMode ? 'bg-[#00001a]/90 border-white/20' : 'bg-white/90 border-gray-200'
          }`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              Add Bank Account
            </h3>
            <div className="space-y-4 mb-6">
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Bank Name
                </label>
                <input
                  type="text"
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode ? 'bg-white/5 text-white border-white/10 focus:border-white/30' : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter bank name"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Account Number
                </label>
                <input
                  type="text"
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode ? 'bg-white/5 text-white border-white/10 focus:border-white/30' : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter account number"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Routing Number
                </label>
                <input
                  type="text"
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode ? 'bg-white/5 text-white border-white/10 focus:border-white/30' : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter routing number"
                />
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowBankModal(false)}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20' : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('🏦 Bank account added successfully')
                  setShowBankModal(false)
                  alert('Bank account added successfully!')
                }}
                className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white text-[#00001a] hover:bg-white/90' : 'bg-[#00001a] text-white hover:bg-[#00001a]/90'
                }`}
              >
                Add Account
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Time Zone Selection Modal */}
      {showTimeZoneModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full max-h-[80vh] overflow-hidden rounded-lg backdrop-blur-xl border ${
            darkMode ? 'bg-[#00001a]/90 border-white/20' : 'bg-white/90 border-gray-200'
          }`}>
            <div className="p-6 border-b border-white/10">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Select Time Zone
              </h3>
              <p className={`text-sm mt-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                Choose your local time zone for scheduling
              </p>
            </div>
            <div className="max-h-96 overflow-y-auto p-4">
              <div className="space-y-2">
                {timeZones.map((timezone) => (
                  <button
                    key={timezone.value}
                    onClick={() => handleTimeZoneChange(timezone.value)}
                    className={`w-full p-3 rounded-lg text-left transition-all duration-300 ${
                      selectedTimeZone === timezone.value
                        ? darkMode
                          ? 'bg-white/15 text-white border border-white/30'
                          : 'bg-[#00001a]/10 text-[#00001a] border border-[#00001a]/20'
                        : darkMode
                          ? 'bg-white/5 text-white/80 border border-white/10 hover:bg-white/10'
                          : 'bg-gray-50 text-[#00001a]/80 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <div className="text-sm font-medium">{timezone.label}</div>
                  </button>
                ))}
              </div>
            </div>
            <div className="p-4 border-t border-white/10">
              <button
                onClick={() => setShowTimeZoneModal(false)}
                className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode ? 'bg-white/10 text-white border border-white/20 hover:bg-white/20' : 'bg-gray-100 text-[#00001a] border border-gray-200 hover:bg-gray-200'
                }`}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Project Modal */}
      {showAddProjectModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg p-6 ${
            darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-gray-200'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Add New Project
              </h3>
              <button
                onClick={() => setShowAddProjectModal(false)}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  darkMode ? 'hover:bg-white/10' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-6">
              {/* Project Title */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Project Title *
                </label>
                <input
                  type="text"
                  value={newProject.title}
                  onChange={(e) => setNewProject(prev => ({ ...prev, title: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                      : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter project title"
                />
              </div>

              {/* Project Description */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Description *
                </label>
                <textarea
                  value={newProject.description}
                  onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 min-h-[120px] ${
                    darkMode
                      ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                      : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Describe your project, its features, and what you learned"
                />
              </div>

              {/* Technologies */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Technologies Used
                </label>
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={newTechnology}
                    onChange={(e) => setNewTechnology(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTechnology())}
                    className={`flex-1 px-4 py-2 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                        : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                    }`}
                    placeholder="Add technology (e.g., React, Node.js)"
                  />
                  <button
                    onClick={() => handleAddTechnology()}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 text-white hover:bg-white/20'
                        : 'bg-gray-100 text-[#00001a] hover:bg-gray-200'
                    }`}
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {newProject.technologies.map((tech) => (
                    <span
                      key={tech}
                      className={`flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
                        darkMode ? 'bg-white/10 text-white/80' : 'bg-gray-100 text-[#00001a]/80'
                      }`}
                    >
                      {tech}
                      <button
                        onClick={() => handleRemoveTechnology(tech)}
                        className="hover:text-red-400"
                      >
                        <X size={12} />
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              {/* Project Link */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Project Link (Optional)
                </label>
                <input
                  type="url"
                  value={newProject.link}
                  onChange={(e) => setNewProject(prev => ({ ...prev, link: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                      : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="https://github.com/username/project"
                />
              </div>

              {/* Status and Duration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Status
                  </label>
                  <select
                    value={newProject.status}
                    onChange={(e) => setNewProject(prev => ({ ...prev, status: e.target.value }))}
                    className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                        : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                    }`}
                  >
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                    <option value="On Hold">On Hold</option>
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Duration
                  </label>
                  <input
                    type="text"
                    value={newProject.duration}
                    onChange={(e) => setNewProject(prev => ({ ...prev, duration: e.target.value }))}
                    className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                        : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                    }`}
                    placeholder="e.g., 3 months, 2 weeks"
                  />
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="flex items-center justify-end gap-4 mt-8">
              <button
                onClick={() => setShowAddProjectModal(false)}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode
                    ? 'text-white/70 hover:bg-white/10'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSaveNewProject}
                className={`flex items-center gap-2 px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 backdrop-blur-sm border ${
                  darkMode
                    ? 'bg-[#00001a] text-white border-white/20 hover:bg-[#00001a]/90'
                    : 'bg-[#00001a] text-white border-[#00001a] hover:bg-[#00001a]/90'
                }`}
              >
                <Save size={16} />
                Save Project
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Project Modal */}
      {showEditProjectModal && editingProject && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg p-6 ${
            darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-gray-200'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Edit Project
              </h3>
              <button
                onClick={() => setShowEditProjectModal(false)}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  darkMode ? 'hover:bg-white/10' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-6">
              {/* Project Title */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Project Title *
                </label>
                <input
                  type="text"
                  value={editingProject.title}
                  onChange={(e) => setEditingProject(prev => ({ ...prev, title: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                      : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Enter project title"
                />
              </div>

              {/* Project Description */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Description *
                </label>
                <textarea
                  value={editingProject.description}
                  onChange={(e) => setEditingProject(prev => ({ ...prev, description: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 min-h-[120px] ${
                    darkMode
                      ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                      : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="Describe your project, its features, and what you learned"
                />
              </div>

              {/* Technologies */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Technologies Used
                </label>
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={newTechnology}
                    onChange={(e) => setNewTechnology(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTechnology(true))}
                    className={`flex-1 px-4 py-2 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                        : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                    }`}
                    placeholder="Add technology (e.g., React, Node.js)"
                  />
                  <button
                    onClick={() => handleAddTechnology(true)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/10 text-white hover:bg-white/20'
                        : 'bg-gray-100 text-[#00001a] hover:bg-gray-200'
                    }`}
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {editingProject.technologies.map((tech) => (
                    <span
                      key={tech}
                      className={`flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
                        darkMode ? 'bg-white/10 text-white/80' : 'bg-gray-100 text-[#00001a]/80'
                      }`}
                    >
                      {tech}
                      <button
                        onClick={() => handleRemoveTechnology(tech, true)}
                        className="hover:text-red-400"
                      >
                        <X size={12} />
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              {/* Project Link */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Project Link (Optional)
                </label>
                <input
                  type="url"
                  value={editingProject.link}
                  onChange={(e) => setEditingProject(prev => ({ ...prev, link: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                      : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                  }`}
                  placeholder="https://github.com/username/project"
                />
              </div>

              {/* Status and Duration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Status
                  </label>
                  <select
                    value={editingProject.status}
                    onChange={(e) => setEditingProject(prev => ({ ...prev, status: e.target.value }))}
                    className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                        : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                    }`}
                  >
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                    <option value="On Hold">On Hold</option>
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Duration
                  </label>
                  <input
                    type="text"
                    value={editingProject.duration}
                    onChange={(e) => setEditingProject(prev => ({ ...prev, duration: e.target.value }))}
                    className={`w-full px-4 py-3 rounded-lg backdrop-blur-sm border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 text-white border-white/10 focus:border-white/30'
                        : 'bg-white text-[#00001a] border-gray-200 focus:border-gray-300'
                    }`}
                    placeholder="e.g., 3 months, 2 weeks"
                  />
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="flex items-center justify-end gap-4 mt-8">
              <button
                onClick={() => setShowEditProjectModal(false)}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                  darkMode
                    ? 'text-white/70 hover:bg-white/10'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEditProject}
                className={`flex items-center gap-2 px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 backdrop-blur-sm border ${
                  darkMode
                    ? 'bg-[#00001a] text-white border-white/20 hover:bg-[#00001a]/90'
                    : 'bg-[#00001a] text-white border-[#00001a] hover:bg-[#00001a]/90'
                }`}
              >
                <Save size={16} />
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Method Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full rounded-lg p-6 ${
            darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-[#00001a]/20'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Add Payment Method
              </h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  darkMode ? 'hover:bg-white/10' : 'hover:bg-[#00001a]/10'
                }`}>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <button className={`w-full p-4 rounded-lg border text-left transition-all duration-300 ${
                darkMode ? 'bg-white/5 border-white/10 hover:bg-white/10' : 'bg-white border-[#00001a]/20 hover:bg-[#00001a]/5'
              }`}>
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    darkMode ? 'bg-white/10 text-white' : 'bg-[#00001a]/10 text-[#00001a]'
                  }`}>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                      <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                      <line x1="1" y1="10" x2="23" y2="10"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>Bank Account</h4>
                    <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>Add your bank details</p>
                  </div>
                </div>
              </button>
              <button className={`w-full p-4 rounded-lg border text-left transition-all duration-300 ${
                darkMode ? 'bg-white/5 border-white/10 hover:bg-white/10' : 'bg-white border-[#00001a]/20 hover:bg-[#00001a]/5'
              }`}>
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    darkMode ? 'bg-white/10 text-white' : 'bg-[#00001a]/10 text-[#00001a]'
                  }`}>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                      <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>UPI</h4>
                    <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>Add UPI ID</p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Transactions Modal */}
      {showTransactionModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-2xl w-full max-h-[80vh] overflow-y-auto rounded-lg p-6 ${
            darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-[#00001a]/20'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                All Transactions
              </h3>
              <button
                onClick={() => setShowTransactionModal(false)}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  darkMode ? 'hover:bg-white/10' : 'hover:bg-[#00001a]/10'
                }`}>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-3">
              {transactions.map((transaction) => (
                <div key={transaction.id} className={`p-4 rounded-lg border ${
                  darkMode ? 'bg-white/5 border-white/10' : 'bg-white border-[#00001a]/20'
                }`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        {transaction.description}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        {transaction.date} • {transaction.status}
                      </p>
                    </div>
                    <p className={`font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      {transaction.type === 'Earning' ? '+' : '-'}${transaction.amount}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Payment Preferences Modal */}
      {showPaymentPrefsModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full rounded-lg p-6 ${
            darkMode ? 'bg-[#00001a] border border-white/20' : 'bg-white border border-[#00001a]/20'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Withdrawal Settings
              </h3>
              <button
                onClick={() => setShowPaymentPrefsModal(false)}
                className={`p-2 rounded-lg transition-all duration-300 ${
                  darkMode ? 'hover:bg-white/10' : 'hover:bg-[#00001a]/10'
                }`}>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Auto Withdrawal
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={paymentPreferences.autoWithdraw}
                    onChange={(e) => handleUpdatePaymentPrefs({ autoWithdraw: e.target.checked })}
                    className="mr-2"
                  />
                  <span className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Enable automatic withdrawals
                  </span>
                </label>
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Withdrawal Threshold ($)
                </label>
                <input
                  type="number"
                  value={paymentPreferences.withdrawalThreshold}
                  onChange={(e) => handleUpdatePaymentPrefs({ withdrawalThreshold: parseInt(e.target.value) })}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-white/5 border-white/10 text-white'
                      : 'bg-white border-[#00001a]/20 text-[#00001a]'
                  }`}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Profile
