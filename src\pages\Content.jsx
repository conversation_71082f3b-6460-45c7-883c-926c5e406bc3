import React, { useState, useRef } from 'react'
import { Plus, X, Package, Star, Play, Heart, MessageCircle, Eye, Share2, Trash2 } from 'lucide-react'

const Content = ({ darkMode }) => {
  const [activeTab, setActiveTab] = useState('All Content')
  const [selectedCategory, setSelectedCategory] = useState('All Domains')
  const [selectedContentType, setSelectedContentType] = useState('All Types')
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showFilters, setShowFilters] = useState(true)
  const [selectedFiles, setSelectedFiles] = useState([])
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef(null)
  const [showPublishModal, setShowPublishModal] = useState(false)
  const [podcastToPublish, setPodcastToPublish] = useState(null)

  // Form states for content creation
  const [contentForm, setContentForm] = useState({
    title: '',
    category: 'Frontend Development',
    subCategory: '',
    description: '',
    tags: '',
    visibility: 'public',
    contentAccess: 'free',
    contentType: 'Blog',
    duration: '',
    episodeNumber: ''
  })

  // UI states
  const [likedContent, setLikedContent] = useState(new Set())
  const [notifications, setNotifications] = useState([])
  const [showNotification, setShowNotification] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState('All')


  // Template categories for reels
  const reelTemplates = [
    { name: 'All', description: 'All content' },
    { name: 'Tutorials', description: 'Step-by-step guides' },
    { name: 'Quick Tips', description: 'Fast learning tips' },
    { name: 'Code Reviews', description: 'Code analysis' },
    { name: 'Tech News', description: 'Latest updates' },
    { name: 'Live Coding', description: 'Real-time coding' },
    { name: 'Interviews', description: 'Developer talks' },
    { name: 'Project Demos', description: 'Showcase projects' },
    { name: 'Behind Scenes', description: 'Development process' },
    { name: 'Q&A', description: 'Questions & answers' }
  ]

  // Content data
  const [contentItems, setContentItems] = useState([
    {
      id: 1,
      title: 'React Performance Optimization Guide',
      type: 'Blog',
      category: 'Frontend Development',
      subCategory: 'React',
      tags: ['react', 'performance', 'optimization'],
      views: 1234,
      likes: 89,
      comments: 23,
      status: 'published',
      createdAt: '2025-07-14',
      thumbnail: null,
      description: 'Complete guide to optimizing React applications for better performance.',
      author: 'pred',
      isPremium: false
    },
    {
      id: 2,
      title: 'JavaScript ES6+ Features',
      type: 'Video',
      category: 'Programming',
      subCategory: 'JavaScript',
      tags: ['javascript', 'es6', 'modern'],
      views: 2156,
      likes: 145,
      comments: 67,
      status: 'published',
      createdAt: '2025-07-12',
      thumbnail: null,
      description: 'Deep dive into modern JavaScript features and best practices.',
      author: 'pred',
      isPremium: true
    },
    {
      id: 3,
      title: 'Node.js API Development',
      type: 'PDF',
      category: 'Backend Development',
      subCategory: 'Node.js',
      tags: ['nodejs', 'api', 'backend'],
      views: 876,
      likes: 54,
      comments: 12,
      status: 'draft',
      createdAt: '2025-07-10',
      thumbnail: null,
      description: 'Complete tutorial on building RESTful APIs with Node.js.',
      author: 'pred',
      isPremium: false
    },
    {
      id: 4,
      title: 'Machine Learning with Python',
      type: 'Video',
      category: 'Data Science',
      subCategory: 'Machine Learning',
      tags: ['python', 'ml', 'ai', 'tensorflow'],
      views: 3421,
      likes: 234,
      comments: 89,
      status: 'published',
      createdAt: '2025-07-08',
      thumbnail: null,
      description: 'Comprehensive course on machine learning algorithms and implementation.',
      author: 'jane_smith',
      isPremium: true
    },
    {
      id: 5,
      title: 'CSS Grid Layout Mastery',
      type: 'Blog',
      category: 'Frontend Development',
      subCategory: 'CSS',
      tags: ['css', 'grid', 'layout', 'responsive'],
      views: 1876,
      likes: 156,
      comments: 45,
      status: 'published',
      createdAt: '2025-07-06',
      thumbnail: null,
      description: 'Master CSS Grid layout with practical examples and real-world projects.',
      author: 'mike_dev',
      isPremium: false
    },
    {
      id: 6,
      title: 'Docker for Beginners',
      type: 'PDF',
      category: 'DevOps',
      subCategory: 'Containerization',
      tags: ['docker', 'containers', 'devops'],
      views: 2341,
      likes: 198,
      comments: 67,
      status: 'published',
      createdAt: '2025-07-04',
      thumbnail: null,
      description: 'Learn Docker fundamentals and containerization best practices.',
      author: 'sarah_ops',
      isPremium: true
    },
    {
      id: 7,
      title: 'React Native Mobile App',
      type: 'GitHub Repo',
      category: 'Mobile Development',
      subCategory: 'React Native',
      tags: ['react-native', 'mobile', 'ios', 'android'],
      views: 987,
      likes: 76,
      comments: 23,
      status: 'published',
      createdAt: '2025-07-02',
      thumbnail: null,
      description: 'Complete React Native app with authentication and real-time features.',
      author: 'alex_mobile',
      isPremium: false
    },
    {
      id: 8,
      title: 'UI/UX Design Principles',
      type: 'Reel',
      category: 'UI/UX Design',
      subCategory: 'Design Theory',
      tags: ['design', 'ux', 'ui', 'principles', 'tutorials'],
      views: 1543,
      likes: 123,
      comments: 34,
      status: 'published',
      createdAt: '2025-06-30',
      thumbnail: null,
      description: 'Essential design principles every designer should know.',
      author: 'pred',
      isPremium: false
    },
    {
      id: 9,
      title: 'React Hooks in 60 Seconds',
      type: 'Reel',
      category: 'Frontend Development',
      subCategory: 'React',
      tags: ['react', 'hooks', 'quicktips', 'tutorial'],
      views: 2876,
      likes: 234,
      comments: 67,
      status: 'published',
      createdAt: '2025-06-28',
      thumbnail: null,
      description: 'Quick tutorial on React Hooks with practical examples.',
      author: 'code_master',
      isPremium: true
    },
    {
      id: 10,
      title: 'CSS Animation Magic',
      type: 'Reel',
      category: 'Frontend Development',
      subCategory: 'CSS',
      tags: ['css', 'animation', 'effects', 'frontend'],
      views: 1987,
      likes: 189,
      comments: 45,
      status: 'published',
      createdAt: '2025-06-26',
      thumbnail: null,
      description: 'Amazing CSS animations you can create in minutes.',
      author: 'design_ninja',
      isPremium: false
    },
    // Additional reels with template-specific content
    {
      id: 11,
      title: 'Code Review Best Practices',
      type: 'Reel',
      category: 'Programming',
      subCategory: 'Best Practices',
      tags: ['codereviews', 'bestpractices', 'programming'],
      views: 1456,
      likes: 98,
      comments: 28,
      status: 'published',
      createdAt: '2025-06-25',
      thumbnail: null,
      description: 'How to conduct effective code reviews.',
      author: 'tech_lead',
      isPremium: false
    },
    {
      id: 12,
      title: 'Latest in AI Development',
      type: 'Reel',
      category: 'Machine Learning',
      subCategory: 'AI',
      tags: ['ai', 'technews', 'machinelearning'],
      views: 3245,
      likes: 287,
      comments: 89,
      status: 'published',
      createdAt: '2025-06-24',
      thumbnail: null,
      description: 'Breaking news in AI and machine learning.',
      author: 'ai_expert',
      isPremium: true
    },
    {
      id: 13,
      title: 'Live Coding Session: Building a Todo App',
      type: 'Reel',
      category: 'Frontend Development',
      subCategory: 'React',
      tags: ['livecoding', 'react', 'tutorial'],
      views: 2134,
      likes: 156,
      comments: 45,
      status: 'published',
      createdAt: '2025-06-23',
      thumbnail: null,
      description: 'Watch me build a complete todo app from scratch.',
      author: 'pred',
      isPremium: false
    },
    {
      id: 14,
      title: 'Developer Interview: Senior Engineer Tips',
      type: 'Reel',
      category: 'Programming',
      subCategory: 'Career',
      tags: ['interviews', 'career', 'tips'],
      views: 1876,
      likes: 134,
      comments: 67,
      status: 'published',
      createdAt: '2025-06-22',
      thumbnail: null,
      description: 'Interview with a senior engineer about career growth.',
      author: 'career_coach',
      isPremium: false
    },
    {
      id: 15,
      title: 'My E-commerce Project Demo',
      type: 'Reel',
      category: 'Web Development',
      subCategory: 'Projects',
      tags: ['projectdemos', 'ecommerce', 'showcase'],
      views: 2567,
      likes: 198,
      comments: 78,
      status: 'published',
      createdAt: '2025-06-21',
      thumbnail: null,
      description: 'Showcasing my latest e-commerce project.',
      author: 'fullstack_dev',
      isPremium: true
    },
    {
      id: 16,
      title: 'Behind the Scenes: App Development Process',
      type: 'Reel',
      category: 'Mobile Development',
      subCategory: 'Process',
      tags: ['behindscenes', 'development', 'process'],
      views: 1234,
      likes: 89,
      comments: 34,
      status: 'published',
      createdAt: '2025-06-20',
      thumbnail: null,
      description: 'See how I develop mobile apps from idea to deployment.',
      author: 'mobile_dev',
      isPremium: false
    },
    {
      id: 17,
      title: 'Q&A: Common React Questions Answered',
      type: 'Reel',
      category: 'Frontend Development',
      subCategory: 'React',
      tags: ['qa', 'react', 'questions'],
      views: 1789,
      likes: 123,
      comments: 56,
      status: 'published',
      createdAt: '2025-06-19',
      thumbnail: null,
      description: 'Answering the most common React questions from the community.',
      author: 'pred',
      isPremium: false
    },
    // Podcast Content
    {
      id: 18,
      title: 'Tech Talk: Future of Web Development',
      type: 'Podcast',
      category: 'Web Development',
      subCategory: 'Industry Trends',
      tags: ['podcast', 'webdev', 'future', 'trends'],
      views: 3456,
      likes: 287,
      comments: 89,
      status: 'published',
      createdAt: '2025-07-15',
      thumbnail: '/api/placeholder/300/200',
      description: 'Deep dive into emerging web technologies and future trends in development.',
      author: 'pred',
      isPremium: true,
      duration: '45:30',
      episodeNumber: 1,
      visibility: 'public',
      recordingFile: 'tech-talk-future-web-dev-1721234567890.mp4'
    },
    {
      id: 19,
      title: 'Code Review Best Practices',
      type: 'Podcast',
      category: 'Programming',
      subCategory: 'Best Practices',
      tags: ['podcast', 'codereviews', 'bestpractices', 'teamwork'],
      views: 2134,
      likes: 198,
      comments: 67,
      status: 'published',
      createdAt: '2025-07-10',
      thumbnail: null,
      description: 'Essential tips for conducting effective code reviews and improving team collaboration.',
      author: 'pred',
      isPremium: false,
      duration: '32:15',
      episodeNumber: 2
    },
    {
      id: 20,
      title: 'AI in Software Development',
      type: 'Podcast',
      category: 'Machine Learning',
      subCategory: 'AI Tools',
      tags: ['podcast', 'ai', 'development', 'tools', 'automation'],
      views: 4567,
      likes: 345,
      comments: 123,
      status: 'published',
      createdAt: '2025-07-05',
      thumbnail: null,
      description: 'Exploring how AI is transforming software development and developer workflows.',
      author: 'tech_expert',
      isPremium: true,
      duration: '52:45',
      episodeNumber: 3
    },
    {
      id: 21,
      title: 'Career Growth for Developers',
      type: 'Podcast',
      category: 'Programming',
      subCategory: 'Career',
      tags: ['podcast', 'career', 'growth', 'developer', 'tips'],
      views: 1876,
      likes: 156,
      comments: 45,
      status: 'draft',
      createdAt: '2025-07-16',
      thumbnail: '/api/placeholder/300/200',
      description: 'Strategies for advancing your career as a software developer.',
      author: 'pred',
      isPremium: false,
      duration: '38:20',
      episodeNumber: 4,
      visibility: 'private',
      recordingFile: 'career-growth-developers-1721234567891.mp4',
      needsPublishing: true
    },
    {
      id: 22,
      title: 'JavaScript Deep Dive: Async Programming',
      type: 'Podcast',
      category: 'Frontend Development',
      subCategory: 'JavaScript',
      tags: ['podcast', 'javascript', 'async', 'promises', 'programming'],
      views: 2341,
      likes: 198,
      comments: 67,
      status: 'published',
      createdAt: '2025-07-17',
      thumbnail: '/api/placeholder/300/200',
      description: 'Comprehensive guide to asynchronous programming in JavaScript.',
      author: 'pred',
      isPremium: true,
      duration: '52:15',
      episodeNumber: 2,
      visibility: 'public',
      recordingFile: 'js-async-programming-1721234567892.mp4'
    },
    {
      id: 23,
      title: 'Building Scalable APIs with Node.js',
      type: 'Podcast',
      category: 'Backend Development',
      subCategory: 'Node.js',
      tags: ['podcast', 'nodejs', 'api', 'scalability', 'backend'],
      views: 1654,
      likes: 134,
      comments: 38,
      status: 'published',
      createdAt: '2025-07-18',
      thumbnail: '/api/placeholder/300/200',
      description: 'Best practices for creating scalable and maintainable APIs.',
      author: 'pred',
      isPremium: false,
      duration: '41:45',
      episodeNumber: 3,
      visibility: 'public',
      recordingFile: 'scalable-apis-nodejs-1721234567893.mp4'
    },
    {
      id: 24,
      title: 'React Performance Optimization Techniques',
      type: 'Podcast',
      category: 'Frontend Development',
      subCategory: 'React',
      tags: ['podcast', 'react', 'performance', 'optimization', 'frontend'],
      views: 987,
      likes: 89,
      comments: 23,
      status: 'draft',
      createdAt: '2025-07-19',
      thumbnail: '/api/placeholder/300/200',
      description: 'Advanced techniques for optimizing React application performance.',
      author: 'pred',
      isPremium: true,
      duration: '36:30',
      episodeNumber: 5,
      visibility: 'private',
      recordingFile: 'react-performance-optimization-1721234567894.mp4',
      needsPublishing: true
    }
  ])

  // Load completed podcasts from localStorage
  React.useEffect(() => {
    const completedPodcasts = JSON.parse(localStorage.getItem('completedPodcasts') || '[]')
    if (completedPodcasts.length > 0) {
      setContentItems(prev => {
        const existingIds = new Set(prev.map(item => item.id))
        const newPodcasts = completedPodcasts.filter(podcast => !existingIds.has(podcast.id))
        return [...prev, ...newPodcasts]
      })
      // Clear the localStorage after loading
      localStorage.removeItem('completedPodcasts')
    }
  }, [])

  const tabs = ['All Content', 'Premium', 'Free', 'Drafts', 'Podcasts']
  const categories = [
    'All Domains',
    'Frontend Development',
    'Backend Development',
    'Mobile Development',
    'Data Science',
    'Machine Learning',
    'DevOps',
    'UI/UX Design',
    'Programming',
    'Web Development'
  ]
  const contentTypes = [
    'All Types',
    'Blog',
    'Video',
    'Reel',
    'PDF',
    'Link',
    'GitHub Repo',
    'Image',
    'Podcast'
  ]

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files)
    setSelectedFiles(files)
    console.log('📁 Selected files:', files)
  }

  const handleUpload = async () => {
    // Validate form
    if (!contentForm.title.trim()) {
      showNotificationMessage('Please enter a title for your content', 'error')
      return
    }

    if (!contentForm.description.trim()) {
      showNotificationMessage('Please enter a description for your content', 'error')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200))
        setUploadProgress(i)
      }

      // Create new content item
      const newContent = {
        id: Date.now(),
        title: contentForm.title,
        type: contentForm.contentType,
        category: contentForm.category,
        subCategory: contentForm.subCategory || 'General',
        tags: contentForm.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        views: 0,
        likes: 0,
        comments: 0,
        status: 'published',
        createdAt: new Date().toISOString().split('T')[0],
        thumbnail: null,
        description: contentForm.description,
        author: 'pred',
        isPremium: contentForm.contentAccess === 'premium',
        ...(contentForm.contentType === 'Podcast' && {
          duration: contentForm.duration,
          episodeNumber: parseInt(contentForm.episodeNumber) || 1
        })
      }

      // Add to content items
      setContentItems(prev => [newContent, ...prev])

      // Reset form
      setContentForm({
        title: '',
        category: 'Frontend Development',
        subCategory: '',
        description: '',
        tags: '',
        visibility: 'public',
        contentAccess: 'free',
        contentType: 'Blog',
        duration: '',
        episodeNumber: ''
      })

      setSelectedFiles([])
      setShowCreateModal(false)
      showNotificationMessage(`"${newContent.title}" has been published successfully! 🎉`, 'success')

    } catch (error) {
      showNotificationMessage('Failed to publish content. Please try again.', 'error')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const handleCreateContent = () => {
    setShowCreateModal(true)
  }

  const handleSaveAsDraft = () => {
    // Validate minimum required fields
    if (!contentForm.title.trim()) {
      showNotificationMessage('Please enter a title to save as draft', 'error')
      return
    }

    // Create draft content item
    const draftContent = {
      id: Date.now(),
      title: contentForm.title,
      type: contentForm.contentType,
      category: contentForm.category,
      subCategory: contentForm.subCategory || 'General',
      tags: contentForm.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      views: 0,
      likes: 0,
      comments: 0,
      status: 'draft',
      createdAt: new Date().toISOString().split('T')[0],
      thumbnail: null,
      description: contentForm.description || 'Draft content - description pending',
      author: 'pred',
      isPremium: contentForm.contentAccess === 'premium'
    }

    // Add to content items
    setContentItems(prev => [draftContent, ...prev])

    // Reset form
    setContentForm({
      title: '',
      category: 'Frontend Development',
      subCategory: '',
      description: '',
      tags: '',
      visibility: 'public',
      contentAccess: 'free',
      contentType: 'Blog',
      duration: '',
      episodeNumber: ''
    })

    setSelectedFiles([])
    setShowCreateModal(false)
    showNotificationMessage(`"${draftContent.title}" has been saved as draft 📝`, 'success')
  }

  // Notification system
  const showNotificationMessage = (message, type = 'success') => {
    const notification = {
      id: Date.now(),
      message,
      type
    }
    setNotifications(prev => [...prev, notification])
    setShowNotification(true)

    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
      if (notifications.length <= 1) {
        setShowNotification(false)
      }
    }, 3000)
  }

  const handleLike = (contentId) => {
    const isLiked = likedContent.has(contentId)

    if (isLiked) {
      // Unlike
      setLikedContent(prev => {
        const newSet = new Set(prev)
        newSet.delete(contentId)
        return newSet
      })
      setContentItems(prev => prev.map(item =>
        item.id === contentId
          ? { ...item, likes: Math.max(0, item.likes - 1) }
          : item
      ))
      showNotificationMessage('Content unliked', 'info')
    } else {
      // Like
      setLikedContent(prev => new Set([...prev, contentId]))
      setContentItems(prev => prev.map(item =>
        item.id === contentId
          ? { ...item, likes: item.likes + 1 }
          : item
      ))
      showNotificationMessage('Content liked!', 'success')
    }
  }

  const handleShare = async (contentId) => {
    const content = contentItems.find(item => item.id === contentId)
    const shareUrl = `${window.location.origin}/content/${contentId}`
    const shareText = `Check out this amazing content: ${content.title} by @${content.author}`

    try {
      if (navigator.share) {
        // Use native share API if available
        await navigator.share({
          title: content.title,
          text: shareText,
          url: shareUrl
        })
        showNotificationMessage('Content shared successfully! 🔗', 'success')
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(`${shareText}\n${shareUrl}`)
        showNotificationMessage('Share link copied to clipboard! 📋', 'success')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      showNotificationMessage('Failed to share content', 'error')
    }
  }

  const handleDelete = (contentId) => {
    const content = contentItems.find(item => item.id === contentId)
    if (window.confirm(`Are you sure you want to delete "${content.title}"? This action cannot be undone.`)) {
      setContentItems(prev => prev.filter(item => item.id !== contentId))
      // Remove from liked content if it was liked
      setLikedContent(prev => {
        const newSet = new Set(prev)
        newSet.delete(contentId)
        return newSet
      })
      showNotificationMessage(`"${content.title}" has been deleted`, 'info')
    }
  }

  const handleAccept = (contentId) => {
    const content = contentItems.find(item => item.id === contentId)
    setContentItems(prev => prev.map(item =>
      item.id === contentId
        ? { ...item, status: 'published' }
        : item
    ))
    showNotificationMessage(`"${content.title}" has been accepted and published! ✅`, 'success')
  }

  const handleDecline = (contentId) => {
    const content = contentItems.find(item => item.id === contentId)
    setContentItems(prev => prev.map(item =>
      item.id === contentId
        ? { ...item, status: 'declined' }
        : item
    ))
    showNotificationMessage(`"${content.title}" has been declined`, 'error')
  }

  const handleViewDetails = (contentId) => {
    const content = contentItems.find(item => item.id === contentId)
    // Increment view count
    setContentItems(prev => prev.map(item =>
      item.id === contentId
        ? { ...item, views: item.views + 1 }
        : item
    ))

    // Create a detailed view (in a real app, this would open a modal or navigate to a detail page)
    const detailsWindow = window.open('', '_blank', 'width=600,height=800')
    detailsWindow.document.write(`
      <html>
        <head>
          <title>${content.title} - Content Details</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
            .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .header { border-bottom: 2px solid #eee; padding-bottom: 15px; margin-bottom: 20px; }
            .title { color: #333; margin: 0 0 10px 0; }
            .meta { color: #666; font-size: 14px; }
            .section { margin: 15px 0; }
            .label { font-weight: bold; color: #555; }
            .tags { display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px; }
            .tag { background: #e3f2fd; color: #1976d2; padding: 3px 8px; border-radius: 12px; font-size: 12px; }
            .stats { display: flex; gap: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px; }
            .stat { text-align: center; }
            .stat-number { font-size: 18px; font-weight: bold; color: #333; }
            .stat-label { font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1 class="title">${content.title}</h1>
              <div class="meta">By @${content.author} • ${content.createdAt}</div>
            </div>

            <div class="section">
              <div class="label">Description:</div>
              <p>${content.description}</p>
            </div>

            <div class="section">
              <div class="label">Category:</div>
              <p>${content.category} > ${content.subCategory}</p>
            </div>

            <div class="section">
              <div class="label">Content Type:</div>
              <p>${content.type}</p>
            </div>

            <div class="section">
              <div class="label">Status:</div>
              <p>${content.status.charAt(0).toUpperCase() + content.status.slice(1)}</p>
            </div>

            <div class="section">
              <div class="label">Access:</div>
              <p>${content.isPremium ? 'Premium' : 'Free'}</p>
            </div>

            ${content.type === 'Podcast' ? `
              <div class="section">
                <div class="label">Duration:</div>
                <p>${content.duration || 'Not specified'}</p>
              </div>

              <div class="section">
                <div class="label">Episode Number:</div>
                <p>${content.episodeNumber || 'Not specified'}</p>
              </div>
            ` : ''}

            <div class="section">
              <div class="label">Tags:</div>
              <div class="tags">
                ${content.tags.map(tag => `<span class="tag">#${tag}</span>`).join('')}
              </div>
            </div>

            <div class="stats">
              <div class="stat">
                <div class="stat-number">${content.views.toLocaleString()}</div>
                <div class="stat-label">Views</div>
              </div>
              <div class="stat">
                <div class="stat-number">${content.likes}</div>
                <div class="stat-label">Likes</div>
              </div>
              <div class="stat">
                <div class="stat-number">${content.comments}</div>
                <div class="stat-label">Comments</div>
              </div>
            </div>
          </div>
        </body>
      </html>
    `)
    detailsWindow.document.close()

    showNotificationMessage(`Viewing details for "${content.title}"`, 'info')
  }

  // Podcast publishing handlers
  const handlePublishPodcast = (podcast) => {
    setPodcastToPublish(podcast)
    setShowPublishModal(true)
  }

  const confirmPublishPodcast = (visibility) => {
    if (podcastToPublish) {
      setContentItems(prev => prev.map(item =>
        item.id === podcastToPublish.id
          ? {
              ...item,
              status: 'published',
              visibility: visibility,
              needsPublishing: false
            }
          : item
      ))
      showNotificationMessage(`Podcast published as ${visibility}!`, 'success')
      setShowPublishModal(false)
      setPodcastToPublish(null)
    }
  }

  // Toggle visibility for published podcasts
  const togglePodcastVisibility = (podcastId) => {
    setContentItems(prev => prev.map(item =>
      item.id === podcastId && item.type === 'Podcast'
        ? {
            ...item,
            visibility: item.visibility === 'public' ? 'private' : 'public'
          }
        : item
    ))
  }

  const filteredContent = contentItems.filter(item => {
    const matchesTab = (activeTab === 'All Content' && item.author === 'pred') ||
                     (activeTab === 'Drafts' && item.status === 'draft' && item.author === 'pred') ||
                     (activeTab === 'Premium' && item.isPremium === true && item.author === 'pred') ||
                     (activeTab === 'Free' && item.isPremium === false && item.author === 'pred') ||
                     (activeTab === 'Podcasts' && item.type === 'Podcast' && item.author === 'pred')

    const matchesCategory = selectedCategory === 'All Domains' || item.category === selectedCategory
    const matchesType = selectedContentType === 'All Types' || item.type === selectedContentType
    const matchesSearch = searchQuery === '' ||
                         item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    // Template filtering for reels and podcasts
    const matchesTemplate = selectedTemplate === 'All' ||
                           (item.type !== 'Reel' && item.type !== 'Podcast') ||
                           (item.type === 'Reel' && (
                             item.title.toLowerCase().includes(selectedTemplate.toLowerCase()) ||
                             item.tags.some(tag => tag.toLowerCase().includes(selectedTemplate.toLowerCase().replace(' ', '')))
                           )) ||
                           (selectedTemplate === 'Podcast' && item.type === 'Podcast')

    return matchesTab && matchesCategory && matchesType && matchesSearch && matchesTemplate
  })

  return (
    <div className={`h-full w-full transition-all duration-500 ${
      darkMode ? 'bg-[#00001a]' : 'bg-white'
    }`}>
      {/* Notification System */}
      {showNotification && notifications.length > 0 && (
        <div className="fixed top-4 right-4 z-50 space-y-2">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`px-4 py-3 rounded-lg shadow-lg backdrop-blur-sm border transition-all duration-300 ${
                notification.type === 'success'
                  ? darkMode
                    ? 'bg-white/20 text-white border-white/30'
                    : 'bg-[#00001a]/10 text-[#00001a] border-[#00001a]/30'
                  : notification.type === 'error'
                  ? darkMode
                    ? 'bg-white/20 text-white border-white/30'
                    : 'bg-[#00001a]/10 text-[#00001a] border-[#00001a]/30'
                  : darkMode
                    ? 'bg-white/20 text-white border-white/30'
                    : 'bg-[#00001a]/10 text-[#00001a] border-[#00001a]/30'
              }`}
            >
              {notification.message}
            </div>
          ))}
        </div>
      )}
      {/* Header */}
      <div className="p-4 pb-0">
        <div className="flex items-center justify-between">
          <div>
            <p className={`text-sm mt-1 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
              Browse tutorials, articles, and videos created by problem solvers
            </p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={handleCreateContent}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-500 relative overflow-hidden ${
                darkMode
                  ? 'bg-white/10 text-white backdrop-blur-md border border-white/20 shadow-[0_0_15px_rgba(59,130,246,0.1)] hover:shadow-[0_0_20px_rgba(59,130,246,0.2)] hover:border-white/30'
                  : 'bg-white text-[#00001a] border border-[#00001a]/30 hover:border-[#00001a]/50'
              }`}
              style={darkMode ? {
                boxShadow: '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)',
                backdropFilter: 'blur(5px)'
              } : {
                boxShadow: 'none',
                transition: 'box-shadow 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = '0_0_20px_rgba(59,130,246,0.2), inset_0_1px_0_rgba(255,255,255,0.1)';
                } else {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.08)';
                }
              }}
              onMouseLeave={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)';
                } else {
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Content
            </button>


          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4 space-y-6">
        {/* Tab Navigation */}
        <div className="flex flex-wrap gap-3">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 cursor-pointer relative overflow-hidden transform hover:-translate-y-1 ${
                activeTab === tab
                  ? darkMode
                    ? 'bg-white text-black font-semibold'
                    : 'bg-[#00001a] text-white hover:bg-[#00001a]/90 font-semibold'
                  : darkMode
                    ? 'bg-[#00001a] text-white font-normal hover:bg-[#00001a]/80'
                    : 'bg-white text-[#00001a] hover:text-[#00001a] font-normal hover:bg-white/90'
              }`}
              style={darkMode ? {
                boxShadow: activeTab === tab
                  ? '0 0 20px rgba(59,130,246,0.15), inset 0 1px 0 rgba(255,255,255,0.1)'
                  : '0 0 15px rgba(59,130,246,0.1), inset 0 1px 0 rgba(255,255,255,0.05)',
                backdropFilter: 'blur(5px)'
              } : {
                boxShadow: activeTab === tab
                  ? '0 8px 25px rgba(0, 0, 26, 0.25), 0 4px 12px rgba(0, 0, 26, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.1)'
                  : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = activeTab === tab
                    ? '0 0 25px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2), 0 10px 25px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255,255,255,0.15)'
                    : '0 0 20px rgba(59, 130, 246, 0.3), 0 0 35px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255,255,255,0.1)';
                } else {
                  e.currentTarget.style.boxShadow = activeTab === tab
                    ? '0 12px 35px rgba(0, 0, 26, 0.35), 0 6px 18px rgba(0, 0, 26, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3), inset 0 -1px 0 rgba(0, 0, 0, 0.15)'
                    : '0 6px 20px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.15)';
                }
              }}
              onMouseLeave={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = activeTab === tab
                    ? '0 0 20px rgba(59,130,246,0.15), inset 0 1px 0 rgba(255,255,255,0.1)'
                    : '0 0 15px rgba(59,130,246,0.1), inset 0 1px 0 rgba(255,255,255,0.05)';
                } else {
                  e.currentTarget.style.boxShadow = activeTab === tab
                    ? '0 8px 25px rgba(0, 0, 26, 0.25), 0 4px 12px rgba(0, 0, 26, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.1)'
                    : '0 4px 15px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)';
                }
              }}
            >
              <div className="flex items-center gap-1.5">
                {tab === 'Podcasts' && (
                  <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" />
                    <path d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5H10.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z" />
                  </svg>
                )}
                <span>{tab}</span>
              </div>
            </button>
          ))}
        </div>

        {/* Filters Section */}
        <div className={`group p-5 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden ${
          darkMode
            ? 'rounded-lg bg-white/3 border-white/20 hover:bg-white/8 hover:border-white/30'
            : 'rounded-2xl bg-white border-[#00001a]/20'
        }`}
        style={darkMode ? {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
          transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
        } : {
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          if (darkMode) {
            e.currentTarget.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
          } else {
            e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
          }
        }}
        onMouseLeave={(e) => {
          if (darkMode) {
            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
          } else {
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
          }
        }}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                Filters
              </h3>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`text-sm font-medium transition-colors duration-300 ${
                darkMode ? 'text-white/70 hover:text-white' : 'text-[#00001a]/70 hover:text-[#00001a]'
              }`}
              style={darkMode ? {
                boxShadow: 'none',
                transition: 'all 0.3s ease'
              } : {
                boxShadow: 'none',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (darkMode) {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08)';
                } else {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              {showFilters ? 'Hide' : 'Show'}
            </button>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Search
                </label>
                <div className="relative">
                  <svg className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <input
                    type="text"
                    placeholder="Search content..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                    }`}
                  />
                </div>
              </div>

              {/* Domain Filter */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Domain
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/5 border-white/20 text-white focus:border-white/40 focus:bg-white/10'
                      : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                  }`}
                >
                  {categories.map(category => (
                    <option key={category} value={category} className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Content Type Filter */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Content Type
                </label>
                <select
                  value={selectedContentType}
                  onChange={(e) => setSelectedContentType(e.target.value)}
                  className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                    darkMode
                      ? 'bg-white/5 border-white/20 text-white focus:border-white/40 focus:bg-white/10'
                      : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                  }`}
                >
                  {contentTypes.map(type => (
                    <option key={type} value={type} className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {/* Reset Filters Button */}
          {(searchQuery || selectedCategory !== 'All Domains' || selectedContentType !== 'All Types') && (
            <div className="mt-4 pt-4 border-t border-white/10">
              <button
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory('All Domains')
                  setSelectedContentType('All Types')
                }}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 border cursor-pointer ${
                  darkMode
                    ? 'bg-white/10 text-white hover:bg-white/20 border-white/20 hover-glow-dark'
                    : 'bg-white text-[#00001a] hover:border-[#00001a]/50 border-[#00001a]/30'
                }`}
                style={{
                  boxShadow: 'none',
                  transition: 'box-shadow 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.08)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                Reset Filters
              </button>
            </div>
          )}
        </div>

        {/* Content Grid */}
        {filteredContent.length === 0 ? (
          <div className={`group p-8 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden text-center ${
            darkMode
              ? 'rounded-lg bg-white/3 border-white/20'
              : 'rounded-2xl bg-white border-[#00001a]/20'
          }`}
          style={darkMode ? {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)'
          } : {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}>
            <div className={`mb-4 ${darkMode ? 'text-white/30' : 'text-[#00001a]/30'}`}>
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </div>
            <h3 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
              No content found
            </h3>
            <p className={`${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
              No content available in this category
            </p>
          </div>
        ) : (
          <>
            {/* Browse Templates Section - Above Reels */}
            <div className="mb-8">
              <h3 className={`text-sm font-medium mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                Browse Templates
              </h3>

              {/* Template Pills - Horizontal Scroll */}
              <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
                {reelTemplates.map((template) => (
                  <button
                    key={template.name}
                    onClick={() => setSelectedTemplate(template.name)}
                    className={`flex-shrink-0 px-4 py-2.5 rounded-lg text-sm font-medium transition-all duration-300 border relative overflow-hidden ${
                      selectedTemplate === template.name
                        ? darkMode
                          ? 'bg-white text-black border-white/20 font-semibold shadow-[0_0_20px_rgba(59,130,246,0.15)]'
                          : 'bg-[#00001a] text-white border-[#00001a] shadow-lg hover:bg-[#00001a]/90 hover:shadow-lg font-medium'
                        : darkMode
                          ? 'bg-transparent text-white border-white/10 font-normal shadow-[0_0_15px_rgba(59,130,246,0.1)] hover:shadow-[0_0_20px_rgba(59,130,246,0.2)] hover:border-white/20'
                          : 'bg-white text-[#00001a]/70 border-[#00001a]/20 hover:text-[#00001a] hover:border-[#00001a]/40'
                    }`}
                    style={darkMode ? {
                      boxShadow: selectedTemplate === template.name
                        ? '0_0_20px_rgba(59,130,246,0.15), inset_0_1px_0_rgba(255,255,255,0.1)'
                        : '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)',
                      backdropFilter: 'blur(5px)'
                    } : {
                      boxShadow: 'none',
                      transition: 'box-shadow 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = selectedTemplate === template.name
                          ? '0_0_25px_rgba(59,130,246,0.25), inset_0_1px_0_rgba(255,255,255,0.15)'
                          : '0_0_20px_rgba(59,130,246,0.2), inset_0_1px_0_rgba(255,255,255,0.1)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.08)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = selectedTemplate === template.name
                          ? '0_0_20px_rgba(59,130,246,0.15), inset_0_1px_0_rgba(255,255,255,0.1)'
                          : '0_0_15px_rgba(59,130,246,0.1), inset_0_1px_0_rgba(255,255,255,0.05)';
                      } else {
                        e.currentTarget.style.boxShadow = 'none';
                      }
                    }}
                  >
                    <span className="whitespace-nowrap">{template.name}</span>
                  </button>
                ))}
              </div>

              {/* Selected Template Description */}
              {selectedTemplate !== 'All' && (
                <div className={`text-xs mt-2 ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                  {reelTemplates.find(t => t.name === selectedTemplate)?.description}
                </div>
              )}
            </div>

            {/* Reels Section - Instagram Style */}
            {filteredContent.some(content => content.type === 'Reel') && (
              <div className="mb-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Reels
                  </h2>
                  <button
                    onClick={handleCreateContent}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 border ${
                      darkMode
                        ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border-blue-500/30'
                        : 'bg-white text-[#00001a] hover:border-[#00001a]/50 border-[#00001a]/30'
                    }`}
                    style={{
                      boxShadow: 'none',
                      transition: 'box-shadow 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.08)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    Create Reel
                  </button>
                </div>
                <div className="flex gap-4 overflow-x-auto pb-4">
                  {filteredContent.filter(content => content.type === 'Reel').map((reel) => (
                    <div
                      key={reel.id}
                      className={`flex-shrink-0 w-64 h-96 rounded-lg overflow-hidden relative cursor-pointer group transition-all duration-300 border ${
                        darkMode
                          ? 'bg-gradient-to-b from-white/10 to-white/5 border-white/20 hover:border-white/30'
                          : 'bg-gradient-to-b from-white to-white border-[#00001a]/20 hover:border-[#00001a]/30'
                      }`}
                      style={{
                        aspectRatio: '9/16',
                        boxShadow: darkMode
                          ? '0 12px 40px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1)'
                          : '0 10px 35px rgba(0, 0, 0, 0.15)',
                        transition: 'box-shadow 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (darkMode) {
                          e.currentTarget.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                        } else {
                          e.currentTarget.style.boxShadow = '0 15px 45px rgba(0, 0, 0, 0.25)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (darkMode) {
                          e.currentTarget.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                        } else {
                          e.currentTarget.style.boxShadow = '0 10px 35px rgba(0, 0, 0, 0.15)';
                        }
                      }}
                    >
                      {/* Reel Background/Thumbnail */}
                      <div className={`absolute inset-0 ${
                        darkMode
                          ? 'bg-gradient-to-b from-white/20 to-white/40'
                          : 'bg-gradient-to-b from-[#00001a]/5 to-[#00001a]/10'
                      }`}>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                            darkMode ? 'bg-white/10 text-white/60' : 'bg-[#00001a]/10 text-[#00001a]/60'
                          }`}>
                            <Play size={32} className={darkMode ? 'text-white' : 'text-[#00001a]'} fill="currentColor" />
                          </div>
                        </div>
                      </div>

                      {/* Play Button Overlay */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className={`w-18 h-18 rounded-full backdrop-blur-md flex items-center justify-center border ${
                          darkMode
                            ? 'bg-white/25 border-white/40 text-white hover:bg-white/35 hover:border-white/50'
                            : 'bg-white/95 border-white/60 text-[#00001a] hover:bg-white hover:border-white/80'
                        } transition-all duration-300`}
                        style={{
                          boxShadow: darkMode
                            ? '0 8px 32px rgba(255, 255, 255, 0.15)'
                            : '0 8px 32px rgba(0, 0, 0, 0.2)'
                        }}>
                          <Play size={40} className={`ml-1 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="currentColor" />
                        </div>
                      </div>

                      {/* Reel Info Overlay */}
                      <div className={`absolute bottom-0 left-0 right-0 p-4 ${
                        darkMode
                          ? 'bg-gradient-to-t from-[#00001a]/95 via-[#00001a]/60 to-transparent'
                          : 'bg-gradient-to-t from-[#00001a]/85 via-[#00001a]/40 to-transparent'
                      }`}>
                        <h3 className={`font-semibold text-sm mb-1 line-clamp-2 ${
                          darkMode ? 'text-white' : 'text-white'
                        }`}>
                          {reel.title}
                        </h3>
                        <p className={`text-xs mb-2 line-clamp-1 ${
                          darkMode ? 'text-white/80' : 'text-white/90'
                        }`}>
                          @{reel.author}
                        </p>

                        {/* Reel Stats */}
                        <div className={`flex items-center gap-3 text-xs ${
                          darkMode ? 'text-white/70' : 'text-white/80'
                        }`}>
                          <span className="flex items-center gap-1">
                            <Eye size={12} className={darkMode ? 'text-white/70' : 'text-white/80'} />
                            {reel.views.toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart size={12} className={darkMode ? 'text-white/70' : 'text-white/80'} fill="currentColor" />
                            {reel.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <MessageCircle size={12} className={darkMode ? 'text-white/70' : 'text-white/80'} fill="currentColor" />
                            {reel.comments}
                          </span>
                        </div>
                      </div>

                      {/* Action Buttons for Reels */}
                      <div className="absolute right-3 bottom-20 flex flex-col gap-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleLike(reel.id);
                          }}
                          className={`w-12 h-12 rounded-full backdrop-blur-md flex items-center justify-center transition-all duration-300 border ${
                            likedContent.has(reel.id)
                              ? darkMode
                                ? 'bg-white/40 border-white/60 text-white hover:bg-white/50'
                                : 'bg-[#00001a]/30 border-[#00001a]/50 text-[#00001a] hover:bg-[#00001a]/40'
                              : darkMode
                                ? 'bg-white/25 border-white/40 text-white hover:bg-white/35 hover:border-white/50'
                                : 'bg-white/95 border-white/60 text-[#00001a] hover:bg-white hover:border-white/80'
                          }`}
                          style={{
                            boxShadow: likedContent.has(reel.id)
                              ? '0 4px 20px rgba(239, 68, 68, 0.3)'
                              : darkMode
                                ? '0 4px 20px rgba(255, 255, 255, 0.1)'
                                : '0 4px 20px rgba(0, 0, 0, 0.15)'
                          }}>
                          <Heart size={24} className={likedContent.has(reel.id) ? 'text-red-500' : 'currentColor'} fill={likedContent.has(reel.id) ? 'currentColor' : 'none'} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShare(reel.id);
                          }}
                          className={`w-12 h-12 rounded-full backdrop-blur-md flex items-center justify-center transition-all duration-300 border ${
                            darkMode
                              ? 'bg-white/25 border-white/40 text-white hover:bg-white/35 hover:border-white/50'
                              : 'bg-white/95 border-white/60 text-[#00001a] hover:bg-white hover:border-white/80'
                          }`}
                          style={{
                            boxShadow: darkMode
                              ? '0 4px 20px rgba(255, 255, 255, 0.1)'
                              : '0 4px 20px rgba(0, 0, 0, 0.15)'
                          }}>
                          <Share2 size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
                        </button>
                        {reel.author === 'pred' && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(reel.id);
                            }}
                            className={`w-12 h-12 rounded-full backdrop-blur-md flex items-center justify-center transition-all duration-300 border ${
                              darkMode
                                ? 'bg-white/30 border-white/50 text-white hover:bg-white/40 hover:border-white/60'
                                : 'bg-[#00001a]/10 border-[#00001a]/20 text-[#00001a] hover:bg-[#00001a]/20 hover:border-[#00001a]/30'
                            }`}
                            style={{
                              boxShadow: '0 4px 20px rgba(239, 68, 68, 0.2)'
                            }}>
                            <Trash2 size={24} className={darkMode ? 'text-white' : 'text-[#00001a]'} />
                          </button>
                        )}
                      </div>

                      {/* Premium Badge for Reels */}
                      {reel.isPremium && (
                        <div className="absolute top-3 left-3">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm border ${
                            darkMode
                              ? 'bg-amber-600/20 text-amber-300 border-amber-500/30'
                              : 'bg-[#00001a] text-white border-[#00001a]'
                          }`}>
                            <span className={darkMode ? '' : 'text-[#00001a] bg-white rounded-full px-1 mr-1'}>👑</span> Premium
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Podcast Section */}
            {(activeTab === 'Podcasts' || selectedTemplate === 'Podcast') && (
              <div className="mb-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredContent.filter(content => content.type === 'Podcast').map((podcast) => (
                    <div
                      key={podcast.id}
                      className={`p-5 backdrop-blur-xl border rounded-lg relative overflow-hidden cursor-pointer podcast-tile ${
                        darkMode
                          ? 'bg-white/3 border-white/20'
                          : 'bg-white border-gray-200 light-mode'
                      }`}
                    >
                      {/* Podcast Thumbnail */}
                      <div className="relative mb-4 rounded-lg overflow-hidden">
                        <div className={`aspect-video flex items-center justify-center ${
                          darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
                        }`}>
                          {podcast.thumbnail ? (
                            <img
                              src={podcast.thumbnail}
                              alt={podcast.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="flex flex-col items-center justify-center">
                              <svg className={`w-12 h-12 mb-2 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`} fill="currentColor" viewBox="0 0 20 20">
                                <path d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" />
                                <path d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5H10.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z" />
                              </svg>
                              <span className={`text-xs ${darkMode ? 'text-white/40' : 'text-[#00001a]/40'}`}>
                                Audio Podcast
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Play Button Overlay */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className={`w-16 h-16 rounded-full backdrop-blur-md flex items-center justify-center border ${
                            darkMode
                              ? 'bg-white/25 border-white/40 text-white hover:bg-white/35 hover:border-white/50'
                              : 'bg-white/95 border-white/60 text-[#00001a] hover:bg-white hover:border-white/80'
                          } transition-all duration-300`}
                          style={{
                            boxShadow: darkMode
                              ? '0 8px 32px rgba(255, 255, 255, 0.15)'
                              : '0 8px 32px rgba(0, 0, 0, 0.2)'
                          }}>
                            <svg className={`w-8 h-8 ml-1 ${darkMode ? 'text-white' : 'text-[#00001a]'}`} fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                      </div>

                      {/* Podcast Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium ${
                            darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-white border border-gray-200 text-[#00001a]'
                          }`}>
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" />
                              <path d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5H10.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z" />
                            </svg>
                            Podcast
                          </span>
                          {podcast.isPremium && (
                            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium ${
                              darkMode ? 'bg-yellow-500/20 text-yellow-300' : 'bg-white border border-gray-200 text-[#00001a]'
                            }`}>
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                              Premium
                            </span>
                          )}
                        </div>

                        {/* Visibility Status */}
                        <div className="flex items-center gap-2">
                          {podcast.status === 'published' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                togglePodcastVisibility(podcast.id);
                              }}
                              className={`inline-flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium transition-all duration-300 ${
                                podcast.visibility === 'public'
                                  ? darkMode
                                    ? 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                                    : 'bg-white border border-gray-200 text-[#00001a] hover:bg-gray-50'
                                  : darkMode
                                    ? 'bg-gray-500/20 text-gray-300 hover:bg-gray-500/30'
                                    : 'bg-white border border-gray-200 text-[#00001a]/60 hover:bg-gray-50'
                              }`}
                              title={`Click to make ${podcast.visibility === 'public' ? 'private' : 'public'}`}
                            >
                              {podcast.visibility === 'public' ? (
                                <>
                                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clipRule="evenodd" />
                                  </svg>
                                  Public
                                </>
                              ) : (
                                <>
                                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                                  </svg>
                                  Private
                                </>
                              )}
                            </button>
                          )}
                        </div>
                      </div>

                      {/* Podcast Title */}
                      <h3 className={`font-bold text-lg mb-2 line-clamp-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        {podcast.title}
                      </h3>

                      {/* Podcast Description */}
                      <p className={`text-sm mb-3 line-clamp-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                        {podcast.description}
                      </p>

                      {/* Podcast Details */}
                      <div className={`flex items-center gap-4 mb-4 text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                          </svg>
                          <span>{podcast.duration}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                          </svg>
                          <span>{new Date(podcast.createdAt).toLocaleDateString()}</span>
                        </div>
                        {podcast.episodeNumber && (
                          <div className="flex items-center gap-1">
                            <span>Ep. {podcast.episodeNumber}</span>
                          </div>
                        )}
                      </div>

                      {/* Podcast Stats */}
                      <div className={`flex items-center gap-4 mb-4 text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        <span className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                          </svg>
                          {podcast.views.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                          </svg>
                          {podcast.likes}
                        </span>
                        <span className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                          </svg>
                          {podcast.comments}
                        </span>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleLike(podcast.id);
                          }}
                          className={`flex-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
                            likedContent.has(podcast.id)
                              ? darkMode
                                ? 'bg-red-500/20 text-red-300 border-red-500/30 hover:bg-red-500/30'
                                : 'bg-red-600 text-white border-red-600 hover:bg-red-700'
                              : darkMode
                                ? 'bg-white/10 text-white border-white/20 hover:bg-white/20'
                                : 'bg-white text-[#00001a] border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {likedContent.has(podcast.id) ? 'Liked' : 'Like'}
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShare(podcast.id);
                          }}
                          className={`flex-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
                            darkMode
                              ? 'bg-white/10 text-white border-white/20 hover:bg-white/20'
                              : 'bg-white text-[#00001a] border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          Share
                        </button>
                        {podcast.needsPublishing && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePublishPodcast(podcast);
                            }}
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
                              darkMode
                                ? 'bg-purple-500/20 text-purple-300 border-purple-500/30 hover:bg-purple-500/30 hover:border-purple-500/40'
                                : 'bg-[#00001a] text-white border-[#00001a] hover:bg-[#00001a]/90 hover:shadow-md'
                            }`}
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
                            </svg>
                            Publish
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Regular Content Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredContent.filter(content => content.type !== 'Reel' && !((selectedTemplate === 'Podcast' || activeTab === 'Podcasts') && content.type === 'Podcast')).map((content) => (
              <div
                key={content.id}
                className={`group p-5 backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden cursor-pointer ${
                  darkMode
                    ? 'rounded-lg bg-white/3 border-white/20 hover:border-blue-400/40'
                    : 'rounded-lg bg-white border-gray-200'
                }`}
                style={darkMode ? {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                } : {
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 0 25px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (darkMode) {
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                  } else {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
              >
                {/* Content Type Badge */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium ${
                      darkMode ? 'bg-blue-500/20 text-blue-300' : 'bg-white border border-gray-200 text-[#00001a]'
                    }`}>
                      {content.type === 'Podcast' && (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" />
                          <path d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5H10.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z" />
                        </svg>
                      )}
                      {content.type}
                    </span>
                    {content.isPremium && (
                      <span className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium ${
                        darkMode ? 'bg-yellow-500/20 text-yellow-300' : 'bg-white border border-gray-200 text-[#00001a]'
                      }`}>
                        Premium
                      </span>
                    )}
                  </div>
                  <span className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium ${
                    content.status === 'published'
                      ? darkMode ? 'bg-green-500/20 text-green-300' : 'bg-white border border-gray-200 text-[#00001a]'
                      : darkMode ? 'bg-orange-500/20 text-orange-300' : 'bg-white border border-gray-200 text-[#00001a]'
                  }`}>
                    {content.status === 'published' ? 'Published' : 'Draft'}
                  </span>
                </div>

                {/* Content Title */}
                <h3 className={`text-lg font-semibold mb-2 line-clamp-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  {content.title}
                </h3>

                {/* Content Description */}
                <p className={`text-sm mb-3 line-clamp-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  {content.description}
                </p>

                {/* Podcast-specific information */}
                {content.type === 'Podcast' && (
                  <div className={`flex items-center gap-4 mb-3 text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                    {content.duration && (
                      <div className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                        <span>{content.duration}</span>
                      </div>
                    )}
                    {content.episodeNumber && (
                      <div className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>Episode {content.episodeNumber}</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Category & Tags */}
                <div className="mb-3">
                  <div className={`text-xs font-medium mb-1 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                    {content.category} • {content.subCategory}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {content.tags.slice(0, 3).map((tag, index) => (
                      <span
                        key={index}
                        className={`inline-flex items-center px-2 py-1 rounded text-xs ${
                          darkMode ? 'bg-blue-500/10 text-blue-200' : 'bg-[#00001a]/5 text-[#00001a]/80'
                        }`}
                      >
                        {tag}
                      </span>
                    ))}
                    {content.tags.length > 3 && (
                      <span className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                        +{content.tags.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4 text-sm">
                    <span className={`flex items-center gap-1 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                      <Eye size={12} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                      {content.views.toLocaleString()}
                    </span>
                    <span className={`flex items-center gap-1 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                      <Heart size={12} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} fill="currentColor" />
                      {content.likes}
                    </span>
                    <span className={`flex items-center gap-1 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                      <MessageCircle size={12} className={darkMode ? 'text-white/60' : 'text-[#00001a]/60'} />
                      {content.comments}
                    </span>
                  </div>
                  <span className={`text-xs ${darkMode ? 'text-white/50' : 'text-[#00001a]/50'}`}>
                    {content.createdAt}
                  </span>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => handleLike(content.id)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
                      likedContent.has(content.id)
                        ? darkMode
                          ? 'bg-blue-500/20 text-blue-300 border-blue-500/30 hover:bg-blue-500/30'
                          : 'bg-white text-[#00001a] border-gray-200 hover:bg-gray-50'
                        : darkMode
                          ? 'bg-blue-500/10 text-blue-200 border-blue-500/20 hover:bg-blue-500/20 hover:border-blue-500/30'
                          : 'bg-white text-[#00001a] border-gray-200 hover:bg-gray-50'
                    }`}
                    style={darkMode ? {
                      boxShadow: 'none',
                      transition: 'all 0.3s ease'
                    } : {
                      boxShadow: 'none',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    {likedContent.has(content.id) ? 'Liked' : 'Like'}
                  </button>
                  <button
                    onClick={() => handleShare(content.id)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
                      darkMode
                        ? 'bg-green-500/20 text-green-300 border-green-500/30 hover:bg-green-500/30 hover:border-green-500/40'
                        : 'bg-[#00001a] text-white border-[#00001a] hover:bg-[#00001a]/90 hover:shadow-md'
                    }`}
                    style={darkMode ? {
                      boxShadow: 'none',
                      transition: 'all 0.3s ease'
                    } : {
                      boxShadow: 'none',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 26, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    Share
                  </button>
                  {content.type === 'Podcast' && content.needsPublishing && (
                    <button
                      onClick={() => handlePublishPodcast(content)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
                        darkMode
                          ? 'bg-purple-500/20 text-purple-300 border-purple-500/30 hover:bg-purple-500/30 hover:border-purple-500/40'
                          : 'bg-[#00001a] text-white border-[#00001a] hover:bg-[#00001a]/90 hover:shadow-md'
                      }`}
                      style={darkMode ? {
                        boxShadow: 'none',
                        transition: 'all 0.3s ease'
                      } : {
                        boxShadow: 'none',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (darkMode) {
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(147, 51, 234, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                        } else {
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(147, 51, 234, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      Publish
                    </button>
                  )}
                  {content.author === 'pred' && (
                    <button
                      onClick={() => handleDelete(content.id)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-md text-xs font-medium transition-all duration-300 border ${
                        darkMode
                          ? 'bg-red-500/20 text-red-300 border-red-500/30 hover:bg-red-500/30 hover:border-red-500/40'
                          : 'bg-white text-[#00001a] border-[#00001a]/60 hover:bg-gray-50 hover:border-[#00001a] shadow-sm'
                      }`}
                      style={darkMode ? {
                        boxShadow: 'none',
                        transition: 'all 0.3s ease'
                      } : {
                        boxShadow: 'none',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (darkMode) {
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                        } else {
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      Delete
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
          </>
        )}


      </div>

      {/* Create Content Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className={`w-full max-w-2xl max-h-[90vh] overflow-y-auto rounded-2xl border shadow-2xl ${
            darkMode
              ? 'bg-[#00001a]/95 border-white/20'
              : 'bg-white border-[#00001a]/20'
          }`}
          style={{
            backdropFilter: 'blur(20px)',
            boxShadow: darkMode
              ? '0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1)'
              : '0 25px 50px rgba(0, 0, 0, 0.25)'
          }}>
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Create New Content
                </h2>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    darkMode
                      ? 'hover:bg-white/10 text-white/70 hover:text-white'
                      : 'hover:bg-[#00001a]/10 text-[#00001a]/70 hover:text-[#00001a]'
                  }`}
                  style={darkMode ? {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  } : {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Content Type Selection */}
              <div className="mb-6">
                <label className={`block text-sm font-medium mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Content Type
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {contentTypes.slice(1).map((type) => (
                    <button
                      key={type}
                      onClick={() => setContentForm(prev => ({ ...prev, contentType: type }))}
                      className={`p-3 rounded-lg border text-sm font-medium transition-all duration-300 ${
                        contentForm.contentType === type
                          ? darkMode
                            ? 'border-blue-500/50 bg-blue-500/20 text-blue-300'
                            : 'border-[#00001a]/50 bg-[#00001a]/20 text-[#00001a]'
                          : darkMode
                            ? 'border-blue-500/20 hover:border-blue-500/40 hover:bg-blue-500/10 text-blue-200'
                            : 'border-[#00001a]/20 hover:border-[#00001a]/30 hover:bg-[#00001a]/5 text-[#00001a]'
                      }`}
                      style={darkMode ? {
                        boxShadow: 'none',
                        transition: 'all 0.3s ease'
                      } : {
                        boxShadow: 'none',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (darkMode) {
                          if (contentForm.contentType === type) {
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3), 0 2px 6px rgba(0, 0, 0, 0.08)';
                          } else {
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                          }
                        } else {
                          if (contentForm.contentType === type) {
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 26, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                          } else {
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                          }
                        }
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      <div className="text-lg mb-1">
                        {type}
                      </div>
                      {type}
                    </button>
                  ))}
                </div>
              </div>

              {/* File Upload */}
              <div className="mb-6">
                <label className={`block text-sm font-medium mb-3 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                  Upload Files
                </label>
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300 ${
                    darkMode
                      ? 'border-white/20 hover:border-white/40 hover:bg-white/5'
                      : 'border-[#00001a]/30 hover:border-[#00001a]/50 hover:bg-[#00001a]/5'
                  }`}
                >
                  <div className={`text-4xl mb-4 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                    <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                  </div>
                  <p className={`text-lg font-medium mb-2 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Drop files here or click to browse
                  </p>
                  <p className={`text-sm ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                    Supports: Videos, Images, PDFs, Documents
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="video/*,image/*,.pdf,.doc,.docx,.txt"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>

                {selectedFiles.length > 0 && (
                  <div className="mt-4">
                    <h4 className={`text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Selected Files ({selectedFiles.length})
                    </h4>
                    <div className="space-y-2">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className={`flex items-center justify-between p-2 rounded border ${
                          darkMode ? 'border-white/10 bg-white/5' : 'border-[#00001a]/20 bg-[#00001a]/5'
                        }`}>
                          <span className={`text-sm ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                            {file.name}
                          </span>
                          <span className={`text-xs ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`}>
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Content Details Form */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {/* Title */}
                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Title
                  </label>
                  <input
                    type="text"
                    placeholder="Enter content title..."
                    value={contentForm.title}
                    onChange={(e) => setContentForm(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                    }`}
                  />
                </div>

                {/* Category */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Category
                  </label>
                  <select
                    value={contentForm.category}
                    onChange={(e) => setContentForm(prev => ({ ...prev, category: e.target.value }))}
                    className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white focus:border-white/40 focus:bg-white/10'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                    }`}>
                    {categories.slice(1).map(category => (
                      <option key={category} value={category} className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Sub-Category */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Sub-Category
                  </label>
                  <input
                    type="text"
                    placeholder="Enter sub-category..."
                    value={contentForm.subCategory}
                    onChange={(e) => setContentForm(prev => ({ ...prev, subCategory: e.target.value }))}
                    className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                    }`}
                  />
                </div>

                {/* Description */}
                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Description
                  </label>
                  <textarea
                    rows={3}
                    placeholder="Describe your content..."
                    value={contentForm.description}
                    onChange={(e) => setContentForm(prev => ({ ...prev, description: e.target.value }))}
                    className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 resize-none ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                    }`}
                  />
                </div>

                {/* Tags */}
                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                    Tags (Hashtags)
                  </label>
                  <input
                    type="text"
                    placeholder="Enter tags separated by commas..."
                    value={contentForm.tags}
                    onChange={(e) => setContentForm(prev => ({ ...prev, tags: e.target.value }))}
                    className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                        : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                    }`}
                  />
                </div>
              </div>

              {/* Content Settings */}
              <div className="mb-6">
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                  Content Settings
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Visibility */}
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Visibility
                    </label>
                    <select
                      value={contentForm.visibility}
                      onChange={(e) => setContentForm(prev => ({ ...prev, visibility: e.target.value }))}
                      className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                        darkMode
                          ? 'bg-white/5 border-white/20 text-white focus:border-white/40 focus:bg-white/10'
                          : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                      }`}>
                      <option value="public" className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>Public</option>
                      <option value="premium" className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>Premium</option>
                      <option value="private" className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>Private</option>
                    </select>
                  </div>

                  {/* Content Type */}
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Content Access
                    </label>
                    <select
                      value={contentForm.contentAccess}
                      onChange={(e) => setContentForm(prev => ({ ...prev, contentAccess: e.target.value }))}
                      className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                        darkMode
                          ? 'bg-white/5 border-white/20 text-white focus:border-white/40 focus:bg-white/10'
                          : 'bg-white border-[#00001a]/30 text-[#00001a] focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                      }`}>
                      <option value="free" className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>Free</option>
                      <option value="premium" className={darkMode ? 'bg-[#00001a] text-white' : 'bg-white text-[#00001a]'}>Premium</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Podcast-specific fields */}
              {contentForm.contentType === 'Podcast' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {/* Duration */}
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Duration (mm:ss)
                    </label>
                    <input
                      type="text"
                      placeholder="e.g., 45:30"
                      value={contentForm.duration}
                      onChange={(e) => setContentForm(prev => ({ ...prev, duration: e.target.value }))}
                      className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                        darkMode
                          ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                          : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                      }`}
                    />
                  </div>

                  {/* Episode Number */}
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      Episode Number
                    </label>
                    <input
                      type="number"
                      placeholder="1"
                      min="1"
                      value={contentForm.episodeNumber}
                      onChange={(e) => setContentForm(prev => ({ ...prev, episodeNumber: e.target.value }))}
                      className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
                        darkMode
                          ? 'bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-white/40 focus:bg-white/10'
                          : 'bg-white border-[#00001a]/30 text-[#00001a] placeholder-[#00001a]/50 focus:border-[#00001a]/50 focus:bg-[#00001a]/5'
                      }`}
                    />
                  </div>
                </div>
              )}

              {/* Upload Progress */}
              {isUploading && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                      Uploading...
                    </span>
                    <span className={`text-sm ${darkMode ? 'text-white/70' : 'text-[#00001a]/70'}`}>
                      {uploadProgress}%
                    </span>
                  </div>
                  <div className={`w-full rounded-full h-2 ${darkMode ? 'bg-white/10' : 'bg-[#00001a]/20'}`}>
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        darkMode ? 'bg-white' : 'bg-[#00001a]'
                      }`}
                      style={{width: `${uploadProgress}%`}}
                    ></div>
                  </div>
                </div>
              )}

              {/* Modal Actions */}
              <div className="flex items-center justify-end gap-3">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                    darkMode
                      ? 'text-white/70 hover:bg-white/5 hover:text-white'
                      : 'text-[#00001a]/70 hover:bg-[#00001a]/10 hover:text-[#00001a]'
                  }`}
                  style={darkMode ? {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  } : {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 255, 255, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveAsDraft}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                    darkMode
                      ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30'
                      : 'bg-[#00001a]/10 text-[#00001a] hover:bg-[#00001a]/20'
                  }`}
                  style={darkMode ? {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  } : {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (darkMode) {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                    } else {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  Save as Draft
                </button>
                <button
                  onClick={handleUpload}
                  disabled={isUploading}
                  className={`px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 backdrop-blur-sm border disabled:opacity-50 disabled:cursor-not-allowed ${
                    darkMode
                      ? 'bg-blue-500/20 text-blue-300 border-blue-500/30 hover:bg-blue-500/30 hover:shadow-lg'
                      : 'bg-[#00001a] text-white border-[#00001a]/20 hover:bg-[#00001a]/90'
                  }`}
                  style={darkMode ? {
                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
                    transition: 'all 0.3s ease'
                  } : {
                    boxShadow: 'none',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!isUploading) {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1)';
                      } else {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 26, 0.2), 0 2px 6px rgba(0, 0, 0, 0.08)';
                      }
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isUploading) {
                      if (darkMode) {
                        e.currentTarget.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
                      } else {
                        e.currentTarget.style.boxShadow = 'none';
                      }
                    }
                  }}
                >
                  {isUploading ? 'Publishing...' : 'Publish Content'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

        {/* Podcast Publish Modal */}
        {showPublishModal && podcastToPublish && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className={`w-full max-w-lg mx-4 rounded-lg border transition-all duration-300 ${
              darkMode
                ? 'bg-[#00001a]/90 border-white/20 backdrop-blur-xl'
                : 'bg-white border-gray-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                    Publish Podcast
                  </h3>
                  <button
                    onClick={() => setShowPublishModal(false)}
                    className={`p-2 rounded-lg transition-colors ${
                      darkMode ? 'hover:bg-white/10 text-white/70' : 'hover:bg-gray-100 text-gray-500'
                    }`}
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>

                {/* Podcast Preview */}
                <div className={`p-4 rounded-lg mb-6 ${
                  darkMode ? 'bg-white/5 border border-white/10' : 'bg-gray-50 border border-gray-200'
                }`}>
                  <div className="flex items-start gap-3">
                    <div className={`w-16 h-16 rounded-lg flex items-center justify-center ${
                      darkMode ? 'bg-white/10' : 'bg-[#00001a]/10'
                    }`}>
                      <svg className={`w-8 h-8 ${darkMode ? 'text-white/60' : 'text-[#00001a]/60'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" />
                        <path d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5H10.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-semibold mb-1 ${darkMode ? 'text-white' : 'text-[#00001a]'}`}>
                        {podcastToPublish.title}
                      </h4>
                      <p className={`text-sm mb-2 ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                        {podcastToPublish.description}
                      </p>
                      <div className={`flex items-center gap-4 text-xs ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                        <span>Duration: {podcastToPublish.duration}</span>
                        <span>Episode: #{podcastToPublish.episodeNumber}</span>
                        <span>Recorded: {new Date(podcastToPublish.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <p className={`mb-6 ${darkMode ? 'text-white/70' : 'text-gray-600'}`}>
                  Choose the visibility setting for your podcast:
                </p>

                <div className="space-y-3">
                  <button
                    onClick={() => confirmPublishPodcast('public')}
                    className={`w-full p-4 rounded-lg border text-left transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white hover:bg-white/10'
                        : 'bg-gray-50 border-gray-200 text-[#00001a] hover:bg-gray-100'
                    }`}
                  >
                    <div className="font-semibold">Publish as Public</div>
                    <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                      Everyone can view and access this podcast
                    </div>
                  </button>

                  <button
                    onClick={() => confirmPublishPodcast('private')}
                    className={`w-full p-4 rounded-lg border text-left transition-all duration-300 ${
                      darkMode
                        ? 'bg-white/5 border-white/20 text-white hover:bg-white/10'
                        : 'bg-gray-50 border-gray-200 text-[#00001a] hover:bg-gray-100'
                    }`}
                  >
                    <div className="font-semibold">Publish as Private</div>
                    <div className={`text-sm ${darkMode ? 'text-white/60' : 'text-gray-500'}`}>
                      Only you can view this podcast
                    </div>
                  </button>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => setShowPublishModal(false)}
                    className={`flex-1 px-4 py-2 rounded-lg border transition-all duration-300 ${
                      darkMode
                        ? 'border-white/20 text-white hover:bg-white/5'
                        : 'border-gray-300 text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

    </div>
  )
}

export default Content
