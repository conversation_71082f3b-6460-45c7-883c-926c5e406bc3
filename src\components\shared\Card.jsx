import React from 'react'

const Card = ({ 
  children, 
  darkMode, 
  className = '', 
  hover = true, 
  onClick = null,
  style = {},
  ...props 
}) => {
  const baseClasses = `
    p-6 rounded-lg backdrop-blur-xl border transition-all duration-500 shadow-xl relative overflow-hidden
    ${darkMode 
      ? 'bg-white/3 border-white/20' 
      : 'bg-white border-gray-200'
    }
    ${hover ? (darkMode
      ? 'hover:border-white/30'
      : 'hover:border-gray-300'
    ) : ''}
    ${onClick ? 'cursor-pointer' : ''}
    ${className}
  `

  const cardStyle = darkMode ? {
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
    transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
    ...style
  } : {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    transition: 'all 0.3s ease',
    ...style
  }

  const handleMouseEnter = (e) => {
    if (hover) {
      if (darkMode) {
        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2)';
      } else {
        e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
      }
    }
  }

  const handleMouseLeave = (e) => {
    if (hover) {
      if (darkMode) {
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
      } else {
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      }
    }
  }

  return (
    <div
      className={baseClasses}
      style={cardStyle}
      onClick={onClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      {children}
    </div>
  )
}

export default Card
